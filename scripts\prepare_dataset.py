"""Script to prepare datasets for image sharpening training."""

import os
import argparse
import requests
from pathlib import Path
import zipfile
import shutil
from tqdm import tqdm
import cv2
import numpy as np


def download_file(url: str, destination: str, description: str = "Downloading"):
    """Download a file with progress bar.
    
    Args:
        url: URL to download from
        destination: Local file path to save to
        description: Description for progress bar
    """
    response = requests.get(url, stream=True)
    response.raise_for_status()
    
    total_size = int(response.headers.get('content-length', 0))
    
    with open(destination, 'wb') as f, tqdm(
        desc=description,
        total=total_size,
        unit='B',
        unit_scale=True,
        unit_divisor=1024,
    ) as pbar:
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                f.write(chunk)
                pbar.update(len(chunk))


def extract_zip(zip_path: str, extract_to: str):
    """Extract a zip file.
    
    Args:
        zip_path: Path to zip file
        extract_to: Directory to extract to
    """
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_to)


def create_sample_images(output_dir: str, num_images: int = 50):
    """Create sample synthetic images for testing.
    
    Args:
        output_dir: Directory to save images
        num_images: Number of images to create
    """
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"Creating {num_images} sample images in {output_dir}")
    
    for i in tqdm(range(num_images), desc="Creating sample images"):
        # Create a random image with various patterns
        height, width = 512, 512
        
        # Random background
        image = np.random.randint(0, 256, (height, width, 3), dtype=np.uint8)
        
        # Add some geometric shapes
        # Rectangle
        cv2.rectangle(image, (50, 50), (200, 150), (255, 0, 0), -1)
        
        # Circle
        cv2.circle(image, (300, 100), 50, (0, 255, 0), -1)
        
        # Text
        cv2.putText(image, f"Sample {i+1}", (50, 300), 
                   cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
        
        # Add some noise for realism
        noise = np.random.normal(0, 25, image.shape)
        image = np.clip(image + noise, 0, 255).astype(np.uint8)
        
        # Save image
        filename = f"sample_{i+1:03d}.png"
        cv2.imwrite(os.path.join(output_dir, filename), image)


def prepare_benchmark_dataset(benchmark_dir: str):
    """Prepare benchmark dataset with different categories.
    
    Args:
        benchmark_dir: Directory to create benchmark dataset
    """
    categories = ["text", "nature", "people", "animals", "games"]
    
    for category in categories:
        category_dir = os.path.join(benchmark_dir, category)
        os.makedirs(category_dir, exist_ok=True)
        
        print(f"Creating sample images for category: {category}")
        
        # Create category-specific sample images
        for i in tqdm(range(25), desc=f"Creating {category} images"):
            height, width = 512, 512
            
            if category == "text":
                # Create text-heavy images
                image = np.ones((height, width, 3), dtype=np.uint8) * 255
                
                # Add multiple lines of text
                for j in range(10):
                    text = f"This is line {j+1} of sample text for testing"
                    y_pos = 50 + j * 40
                    cv2.putText(image, text, (20, y_pos), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
                
            elif category == "nature":
                # Create nature-like patterns
                image = np.random.randint(50, 200, (height, width, 3), dtype=np.uint8)
                
                # Add tree-like structures
                for _ in range(5):
                    x = np.random.randint(0, width)
                    y = np.random.randint(height//2, height)
                    cv2.line(image, (x, y), (x, y-100), (34, 139, 34), 5)
                    
                # Add some circular "leaves"
                for _ in range(20):
                    x = np.random.randint(0, width)
                    y = np.random.randint(0, height//2)
                    cv2.circle(image, (x, y), 10, (0, 255, 0), -1)
                    
            elif category == "people":
                # Create simple face-like structures
                image = np.random.randint(200, 255, (height, width, 3), dtype=np.uint8)
                
                # Simple face outline
                center = (width//2, height//2)
                cv2.circle(image, center, 100, (255, 220, 177), -1)
                
                # Eyes
                cv2.circle(image, (center[0]-30, center[1]-20), 10, (0, 0, 0), -1)
                cv2.circle(image, (center[0]+30, center[1]-20), 10, (0, 0, 0), -1)
                
                # Mouth
                cv2.ellipse(image, (center[0], center[1]+30), (20, 10), 0, 0, 180, (0, 0, 0), 2)
                
            elif category == "animals":
                # Create simple animal-like shapes
                image = np.random.randint(100, 200, (height, width, 3), dtype=np.uint8)
                
                # Simple animal body
                cv2.ellipse(image, (width//2, height//2), (80, 50), 0, 0, 360, (139, 69, 19), -1)
                
                # Head
                cv2.circle(image, (width//2, height//2-60), 40, (139, 69, 19), -1)
                
                # Ears
                cv2.circle(image, (width//2-25, height//2-80), 15, (139, 69, 19), -1)
                cv2.circle(image, (width//2+25, height//2-80), 15, (139, 69, 19), -1)
                
            elif category == "games":
                # Create game-like graphics
                image = np.zeros((height, width, 3), dtype=np.uint8)
                
                # Checkerboard pattern
                for row in range(0, height, 64):
                    for col in range(0, width, 64):
                        if (row//64 + col//64) % 2 == 0:
                            image[row:row+64, col:col+64] = [255, 0, 0]
                        else:
                            image[row:row+64, col:col+64] = [0, 0, 255]
                
                # Add some game elements
                cv2.circle(image, (width//4, height//4), 30, (255, 255, 0), -1)
                cv2.rectangle(image, (3*width//4-20, 3*height//4-20), 
                             (3*width//4+20, 3*height//4+20), (0, 255, 0), -1)
            
            # Save image
            filename = f"{category}_{i+1:03d}.png"
            cv2.imwrite(os.path.join(category_dir, filename), image)


def main():
    """Main function to prepare datasets."""
    parser = argparse.ArgumentParser(description='Prepare datasets for image sharpening')
    parser.add_argument('--data-root', type=str, default='data',
                       help='Root directory for datasets')
    parser.add_argument('--create-samples', action='store_true',
                       help='Create synthetic sample images')
    parser.add_argument('--num-train', type=int, default=1000,
                       help='Number of training images to create')
    parser.add_argument('--num-val', type=int, default=200,
                       help='Number of validation images to create')
    parser.add_argument('--num-test', type=int, default=100,
                       help='Number of test images to create')
    
    args = parser.parse_args()
    
    # Create directory structure
    data_root = Path(args.data_root)
    train_dir = data_root / 'train'
    val_dir = data_root / 'val'
    test_dir = data_root / 'test'
    benchmark_dir = data_root / 'benchmark'
    
    for dir_path in [train_dir, val_dir, test_dir, benchmark_dir]:
        dir_path.mkdir(parents=True, exist_ok=True)
    
    if args.create_samples:
        print("Creating synthetic sample datasets...")
        
        # Create training images
        create_sample_images(str(train_dir), args.num_train)
        
        # Create validation images
        create_sample_images(str(val_dir), args.num_val)
        
        # Create test images
        create_sample_images(str(test_dir), args.num_test)
        
        # Create benchmark dataset
        prepare_benchmark_dataset(str(benchmark_dir))
        
        print("Sample datasets created successfully!")
        print(f"Training images: {len(list(train_dir.glob('*.png')))}")
        print(f"Validation images: {len(list(val_dir.glob('*.png')))}")
        print(f"Test images: {len(list(test_dir.glob('*.png')))}")
        print(f"Benchmark categories: {len(list(benchmark_dir.iterdir()))}")
    
    print("\nDataset preparation completed!")
    print("\nTo use real datasets, please:")
    print("1. Download high-quality image datasets (e.g., DIV2K, Flickr2K)")
    print("2. Place images in the respective directories:")
    print(f"   - Training: {train_dir}")
    print(f"   - Validation: {val_dir}")
    print(f"   - Test: {test_dir}")
    print(f"   - Benchmark: {benchmark_dir}/[category]/")


if __name__ == '__main__':
    main()
