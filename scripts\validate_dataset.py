"""<PERSON>ript to validate and analyze datasets."""

import os
import argparse
from pathlib import Path
import cv2
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt
from collections import defaultdict
import sys

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.data.dataset import SharpnessDataset, BenchmarkDataset
from src.utils.config import load_config


def analyze_images(image_dir: str) -> dict:
    """Analyze images in a directory.
    
    Args:
        image_dir: Directory containing images
        
    Returns:
        Dictionary with analysis results
    """
    if not os.path.exists(image_dir):
        return {"error": f"Directory {image_dir} does not exist"}
    
    image_files = []
    for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
        image_files.extend([
            os.path.join(image_dir, f)
            for f in os.listdir(image_dir)
            if f.lower().endswith(ext)
        ])
    
    if not image_files:
        return {"error": f"No images found in {image_dir}"}
    
    analysis = {
        "total_images": len(image_files),
        "resolutions": defaultdict(int),
        "aspect_ratios": defaultdict(int),
        "file_sizes": [],
        "channels": defaultdict(int),
        "corrupted_files": []
    }
    
    print(f"Analyzing {len(image_files)} images in {image_dir}")
    
    for img_path in tqdm(image_files, desc="Analyzing images"):
        try:
            # Load image
            img = cv2.imread(img_path)
            if img is None:
                analysis["corrupted_files"].append(img_path)
                continue
            
            # Get image properties
            h, w, c = img.shape
            resolution = f"{w}x{h}"
            aspect_ratio = round(w / h, 2)
            file_size = os.path.getsize(img_path) / (1024 * 1024)  # MB
            
            # Update statistics
            analysis["resolutions"][resolution] += 1
            analysis["aspect_ratios"][aspect_ratio] += 1
            analysis["file_sizes"].append(file_size)
            analysis["channels"][c] += 1
            
        except Exception as e:
            analysis["corrupted_files"].append(f"{img_path}: {str(e)}")
    
    # Calculate statistics
    if analysis["file_sizes"]:
        analysis["avg_file_size_mb"] = np.mean(analysis["file_sizes"])
        analysis["total_size_mb"] = sum(analysis["file_sizes"])
    
    return analysis


def test_dataset_loading(config, data_dir: str, dataset_type: str = "train"):
    """Test dataset loading functionality.
    
    Args:
        config: Configuration object
        data_dir: Data directory
        dataset_type: Type of dataset (train/val/test)
    """
    print(f"\nTesting {dataset_type} dataset loading...")
    
    try:
        # Create dataset
        dataset = SharpnessDataset(
            data_dir=data_dir,
            input_size=config.get('dataset.input_size', [256, 256]),
            apply_degradation=True,
            augment=(dataset_type == 'train')
        )
        
        print(f"Dataset size: {len(dataset)}")
        
        # Test loading a few samples
        num_samples = min(5, len(dataset))
        print(f"Testing {num_samples} samples...")
        
        for i in range(num_samples):
            try:
                lr_img, hr_img = dataset[i]
                print(f"Sample {i+1}: LR shape {lr_img.shape}, HR shape {hr_img.shape}")
                
                # Check tensor properties
                assert lr_img.dtype == torch.float32, f"Expected float32, got {lr_img.dtype}"
                assert hr_img.dtype == torch.float32, f"Expected float32, got {hr_img.dtype}"
                assert 0 <= lr_img.min() <= lr_img.max() <= 1, f"LR values out of range [0,1]"
                assert 0 <= hr_img.min() <= hr_img.max() <= 1, f"HR values out of range [0,1]"
                
            except Exception as e:
                print(f"Error loading sample {i+1}: {e}")
        
        print(f"✓ {dataset_type} dataset loading test passed")
        
    except Exception as e:
        print(f"✗ {dataset_type} dataset loading test failed: {e}")


def test_benchmark_dataset(config, benchmark_dir: str):
    """Test benchmark dataset loading.
    
    Args:
        config: Configuration object
        benchmark_dir: Benchmark data directory
    """
    print("\nTesting benchmark dataset loading...")
    
    try:
        categories = config.get('evaluation.categories', ["text", "nature", "people", "animals", "games"])
        min_images = config.get('evaluation.min_images_per_category', 20)
        
        dataset = BenchmarkDataset(
            data_dir=benchmark_dir,
            categories=categories,
            min_images_per_category=min_images
        )
        
        print(f"Benchmark dataset size: {len(dataset)}")
        
        # Test loading samples from each category
        category_counts = defaultdict(int)
        num_samples = min(10, len(dataset))
        
        for i in range(num_samples):
            try:
                img_tensor, category = dataset[i]
                category_counts[category] += 1
                print(f"Sample {i+1}: {category}, shape {img_tensor.shape}")
                
            except Exception as e:
                print(f"Error loading benchmark sample {i+1}: {e}")
        
        print(f"Category distribution in samples: {dict(category_counts)}")
        print("✓ Benchmark dataset loading test passed")
        
    except Exception as e:
        print(f"✗ Benchmark dataset loading test failed: {e}")


def create_visualization(analysis: dict, output_path: str):
    """Create visualization of dataset analysis.
    
    Args:
        analysis: Analysis results
        output_path: Path to save visualization
    """
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Dataset Analysis', fontsize=16)
    
    # Resolution distribution
    if analysis["resolutions"]:
        resolutions = list(analysis["resolutions"].keys())[:10]  # Top 10
        counts = [analysis["resolutions"][r] for r in resolutions]
        
        axes[0, 0].bar(range(len(resolutions)), counts)
        axes[0, 0].set_title('Top 10 Resolutions')
        axes[0, 0].set_xticks(range(len(resolutions)))
        axes[0, 0].set_xticklabels(resolutions, rotation=45)
        axes[0, 0].set_ylabel('Count')
    
    # Aspect ratio distribution
    if analysis["aspect_ratios"]:
        ratios = sorted(analysis["aspect_ratios"].keys())
        counts = [analysis["aspect_ratios"][r] for r in ratios]
        
        axes[0, 1].bar(range(len(ratios)), counts)
        axes[0, 1].set_title('Aspect Ratio Distribution')
        axes[0, 1].set_xlabel('Aspect Ratio')
        axes[0, 1].set_ylabel('Count')
    
    # File size distribution
    if analysis["file_sizes"]:
        axes[1, 0].hist(analysis["file_sizes"], bins=20, alpha=0.7)
        axes[1, 0].set_title('File Size Distribution')
        axes[1, 0].set_xlabel('File Size (MB)')
        axes[1, 0].set_ylabel('Count')
    
    # Channel distribution
    if analysis["channels"]:
        channels = list(analysis["channels"].keys())
        counts = list(analysis["channels"].values())
        
        axes[1, 1].pie(counts, labels=[f'{c} channels' for c in channels], autopct='%1.1f%%')
        axes[1, 1].set_title('Channel Distribution')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Visualization saved to {output_path}")


def main():
    """Main validation function."""
    parser = argparse.ArgumentParser(description='Validate and analyze datasets')
    parser.add_argument('--config', type=str, default='config.yaml',
                       help='Path to configuration file')
    parser.add_argument('--data-root', type=str, default='data',
                       help='Root directory for datasets')
    parser.add_argument('--output-dir', type=str, default='results',
                       help='Output directory for analysis results')
    
    args = parser.parse_args()
    
    # Load configuration
    try:
        config = load_config(args.config)
    except Exception as e:
        print(f"Error loading config: {e}")
        return
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Dataset directories
    data_root = Path(args.data_root)
    train_dir = data_root / 'train'
    val_dir = data_root / 'val'
    test_dir = data_root / 'test'
    benchmark_dir = data_root / 'benchmark'
    
    print("Dataset Validation and Analysis")
    print("=" * 50)
    
    # Analyze each dataset
    datasets = [
        ("Training", str(train_dir)),
        ("Validation", str(val_dir)),
        ("Test", str(test_dir)),
        ("Benchmark", str(benchmark_dir))
    ]
    
    all_analysis = {}
    
    for name, directory in datasets:
        print(f"\n{name} Dataset Analysis:")
        print("-" * 30)
        
        analysis = analyze_images(directory)
        all_analysis[name] = analysis
        
        if "error" in analysis:
            print(f"Error: {analysis['error']}")
            continue
        
        print(f"Total images: {analysis['total_images']}")
        print(f"Corrupted files: {len(analysis['corrupted_files'])}")
        
        if analysis.get("avg_file_size_mb"):
            print(f"Average file size: {analysis['avg_file_size_mb']:.2f} MB")
            print(f"Total dataset size: {analysis['total_size_mb']:.2f} MB")
        
        # Show top resolutions
        if analysis["resolutions"]:
            top_resolutions = sorted(analysis["resolutions"].items(), 
                                   key=lambda x: x[1], reverse=True)[:5]
            print("Top resolutions:")
            for res, count in top_resolutions:
                print(f"  {res}: {count} images")
        
        # Create visualization
        if analysis["total_images"] > 0:
            viz_path = os.path.join(args.output_dir, f"{name.lower()}_analysis.png")
            create_visualization(analysis, viz_path)
    
    # Test dataset loading functionality
    print("\n" + "=" * 50)
    print("Dataset Loading Tests")
    print("=" * 50)
    
    # Import torch here to avoid import errors if not installed
    try:
        import torch
        
        # Test training dataset
        if train_dir.exists():
            test_dataset_loading(config, str(train_dir), "train")
        
        # Test validation dataset
        if val_dir.exists():
            test_dataset_loading(config, str(val_dir), "val")
        
        # Test benchmark dataset
        if benchmark_dir.exists():
            test_benchmark_dataset(config, str(benchmark_dir))
            
    except ImportError:
        print("PyTorch not installed, skipping dataset loading tests")
    
    print("\nValidation completed!")


if __name__ == '__main__':
    main()
