"""Real-time image sharpening demo."""

import os
import argparse
import cv2
import numpy as np
import time
import sys
from collections import deque

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.inference.optimized_inference import OptimizedInferenceEngine


class RealTimeDemo:
    """Real-time image sharpening demo."""
    
    def __init__(self, 
                 model_path: str,
                 device: str = 'cuda',
                 show_fps: bool = True,
                 show_comparison: bool = True):
        """Initialize real-time demo.
        
        Args:
            model_path: Path to trained model
            device: Device for inference
            show_fps: Whether to show FPS counter
            show_comparison: Whether to show side-by-side comparison
        """
        self.show_fps = show_fps
        self.show_comparison = show_comparison
        
        # Initialize inference engine
        self.engine = OptimizedInferenceEngine(
            model_path=model_path,
            device=device,
            use_half_precision=(device == 'cuda')
        )
        
        # FPS tracking
        self.fps_history = deque(maxlen=30)
        self.frame_times = deque(maxlen=30)
        
        print(f"Initialized real-time demo with model: {model_path}")
        print(f"Device: {device}")
        print("Press 'q' to quit, 's' to save screenshot, 'c' to toggle comparison")
    
    def process_frame(self, frame: np.ndarray) -> tuple:
        """Process a single frame.
        
        Args:
            frame: Input frame
            
        Returns:
            Tuple of (processed_frame, inference_time)
        """
        start_time = time.time()
        
        # Run inference
        processed_frame, inference_time = self.engine.infer_image(frame)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Update FPS tracking
        self.frame_times.append(total_time)
        if total_time > 0:
            self.fps_history.append(1.0 / total_time)
        
        return processed_frame, inference_time
    
    def add_fps_overlay(self, frame: np.ndarray) -> np.ndarray:
        """Add FPS overlay to frame.
        
        Args:
            frame: Input frame
            
        Returns:
            Frame with FPS overlay
        """
        if not self.show_fps or len(self.fps_history) == 0:
            return frame
        
        # Calculate average FPS
        avg_fps = np.mean(list(self.fps_history))
        avg_time = np.mean(list(self.frame_times)) * 1000  # ms
        
        # Add text overlay
        fps_text = f"FPS: {avg_fps:.1f}"
        time_text = f"Time: {avg_time:.1f}ms"
        
        # Background rectangle for text
        cv2.rectangle(frame, (10, 10), (250, 80), (0, 0, 0), -1)
        cv2.rectangle(frame, (10, 10), (250, 80), (255, 255, 255), 2)
        
        # Add text
        cv2.putText(frame, fps_text, (20, 35), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(frame, time_text, (20, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        return frame
    
    def create_comparison_view(self, 
                              original: np.ndarray, 
                              processed: np.ndarray) -> np.ndarray:
        """Create side-by-side comparison view.
        
        Args:
            original: Original frame
            processed: Processed frame
            
        Returns:
            Combined comparison frame
        """
        # Ensure both frames have the same size
        if original.shape != processed.shape:
            processed = cv2.resize(processed, (original.shape[1], original.shape[0]))
        
        # Add labels
        original_labeled = original.copy()
        processed_labeled = processed.copy()
        
        cv2.putText(original_labeled, "Original", (20, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
        cv2.putText(processed_labeled, "Sharpened", (20, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        # Combine horizontally
        comparison = np.hstack([original_labeled, processed_labeled])
        
        return comparison
    
    def run_webcam_demo(self, camera_id: int = 0):
        """Run real-time demo with webcam.
        
        Args:
            camera_id: Camera device ID
        """
        # Initialize camera
        cap = cv2.VideoCapture(camera_id)
        
        if not cap.isOpened():
            print(f"Error: Could not open camera {camera_id}")
            return
        
        # Set camera properties for better performance
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        print("Starting webcam demo...")
        print("Controls:")
        print("  'q' - Quit")
        print("  's' - Save screenshot")
        print("  'c' - Toggle comparison view")
        print("  'f' - Toggle FPS display")
        
        screenshot_counter = 0
        
        while True:
            # Capture frame
            ret, frame = cap.read()
            if not ret:
                print("Error: Could not read frame")
                break
            
            # Process frame
            processed_frame, inference_time = self.process_frame(frame)
            
            # Create display frame
            if self.show_comparison:
                display_frame = self.create_comparison_view(frame, processed_frame)
            else:
                display_frame = processed_frame
            
            # Add FPS overlay
            display_frame = self.add_fps_overlay(display_frame)
            
            # Show frame
            cv2.imshow('Real-time Image Sharpening', display_frame)
            
            # Handle key presses
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q'):
                break
            elif key == ord('s'):
                # Save screenshot
                timestamp = int(time.time())
                if self.show_comparison:
                    filename = f"screenshot_comparison_{timestamp}.png"
                    cv2.imwrite(filename, display_frame)
                else:
                    filename = f"screenshot_original_{timestamp}.png"
                    cv2.imwrite(filename, frame)
                    filename_processed = f"screenshot_processed_{timestamp}.png"
                    cv2.imwrite(filename_processed, processed_frame)
                
                screenshot_counter += 1
                print(f"Screenshot saved: {filename}")
                
            elif key == ord('c'):
                # Toggle comparison view
                self.show_comparison = not self.show_comparison
                print(f"Comparison view: {'ON' if self.show_comparison else 'OFF'}")
                
            elif key == ord('f'):
                # Toggle FPS display
                self.show_fps = not self.show_fps
                print(f"FPS display: {'ON' if self.show_fps else 'OFF'}")
        
        # Cleanup
        cap.release()
        cv2.destroyAllWindows()
        
        # Print final statistics
        if len(self.fps_history) > 0:
            avg_fps = np.mean(list(self.fps_history))
            min_fps = np.min(list(self.fps_history))
            max_fps = np.max(list(self.fps_history))
            
            print(f"\nFinal Statistics:")
            print(f"Average FPS: {avg_fps:.1f}")
            print(f"Min FPS: {min_fps:.1f}")
            print(f"Max FPS: {max_fps:.1f}")
            print(f"Screenshots saved: {screenshot_counter}")
    
    def run_video_demo(self, input_path: str, output_path: str = None):
        """Run demo on video file.
        
        Args:
            input_path: Path to input video
            output_path: Path to save output video (optional)
        """
        # Open input video
        cap = cv2.VideoCapture(input_path)
        
        if not cap.isOpened():
            print(f"Error: Could not open video {input_path}")
            return
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"Processing video: {input_path}")
        print(f"Resolution: {width}x{height}")
        print(f"FPS: {fps}")
        print(f"Total frames: {total_frames}")
        
        # Setup output video if specified
        out = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            if self.show_comparison:
                out = cv2.VideoWriter(output_path, fourcc, fps, (width * 2, height))
            else:
                out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Process frame
            processed_frame, inference_time = self.process_frame(frame)
            
            # Create display frame
            if self.show_comparison:
                display_frame = self.create_comparison_view(frame, processed_frame)
            else:
                display_frame = processed_frame
            
            # Add FPS overlay
            display_frame = self.add_fps_overlay(display_frame)
            
            # Save frame if output specified
            if out:
                out.write(display_frame)
            
            # Show frame (optional for video processing)
            cv2.imshow('Video Processing', display_frame)
            
            frame_count += 1
            
            # Progress update
            if frame_count % 30 == 0:
                progress = (frame_count / total_frames) * 100
                avg_fps = np.mean(list(self.fps_history)) if self.fps_history else 0
                print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames}), "
                      f"Processing FPS: {avg_fps:.1f}")
            
            # Allow early exit
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        # Cleanup
        cap.release()
        if out:
            out.release()
        cv2.destroyAllWindows()
        
        print(f"Video processing completed!")
        if output_path:
            print(f"Output saved to: {output_path}")


def main():
    """Main demo function."""
    parser = argparse.ArgumentParser(description='Real-time image sharpening demo')
    parser.add_argument('--model-path', type=str, required=True,
                       help='Path to trained model')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cpu', 'cuda'], help='Device to use')
    parser.add_argument('--mode', type=str, choices=['webcam', 'video'],
                       default='webcam', help='Demo mode')
    parser.add_argument('--input-video', type=str, default=None,
                       help='Input video path (for video mode)')
    parser.add_argument('--output-video', type=str, default=None,
                       help='Output video path (for video mode)')
    parser.add_argument('--camera-id', type=int, default=0,
                       help='Camera device ID (for webcam mode)')
    parser.add_argument('--no-comparison', action='store_true',
                       help='Disable side-by-side comparison')
    parser.add_argument('--no-fps', action='store_true',
                       help='Disable FPS display')
    
    args = parser.parse_args()
    
    # Set device
    device = args.device
    if device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        device = 'cpu'
    
    # Check model
    if not os.path.exists(args.model_path):
        print(f"Model not found: {args.model_path}")
        return
    
    print("Real-time Image Sharpening Demo")
    print("=" * 40)
    print(f"Model: {args.model_path}")
    print(f"Device: {device}")
    print(f"Mode: {args.mode}")
    
    try:
        # Initialize demo
        demo = RealTimeDemo(
            model_path=args.model_path,
            device=device,
            show_fps=not args.no_fps,
            show_comparison=not args.no_comparison
        )
        
        # Run demo
        if args.mode == 'webcam':
            demo.run_webcam_demo(args.camera_id)
        elif args.mode == 'video':
            if not args.input_video:
                print("Error: --input-video required for video mode")
                return
            
            if not os.path.exists(args.input_video):
                print(f"Input video not found: {args.input_video}")
                return
            
            demo.run_video_demo(args.input_video, args.output_video)
        
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
