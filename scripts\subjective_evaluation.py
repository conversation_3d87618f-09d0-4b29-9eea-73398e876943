"""Script for conducting subjective evaluation and MOS studies."""

import os
import argparse
import json
import sys

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.evaluation.subjective import (
    SubjectiveEvaluationInterface, 
    MOSAnalyzer, 
    prepare_subjective_evaluation
)


def main():
    """Main subjective evaluation function."""
    parser = argparse.ArgumentParser(description='Subjective evaluation for image sharpening')
    parser.add_argument('--mode', type=str, choices=['prepare', 'evaluate', 'analyze'],
                       required=True, help='Mode of operation')
    
    # Common arguments
    parser.add_argument('--output-dir', type=str, default='results/subjective',
                       help='Output directory')
    
    # Prepare mode arguments
    parser.add_argument('--model-path', type=str, default=None,
                       help='Path to trained model (for prepare mode)')
    parser.add_argument('--benchmark-dir', type=str, default='data/benchmark',
                       help='Benchmark dataset directory (for prepare mode)')
    parser.add_argument('--num-images', type=int, default=50,
                       help='Number of images to prepare (for prepare mode)')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cpu', 'cuda'], help='Device to use')
    
    # Evaluate mode arguments
    parser.add_argument('--image-pairs', type=str, default=None,
                       help='Path to image pairs file (for evaluate mode)')
    parser.add_argument('--evaluator-id', type=str, default=None,
                       help='Unique evaluator identifier (for evaluate mode)')
    
    # Analyze mode arguments
    parser.add_argument('--results-dir', type=str, default=None,
                       help='Directory containing evaluation results (for analyze mode)')
    
    args = parser.parse_args()
    
    if args.mode == 'prepare':
        prepare_mode(args)
    elif args.mode == 'evaluate':
        evaluate_mode(args)
    elif args.mode == 'analyze':
        analyze_mode(args)


def prepare_mode(args):
    """Prepare images for subjective evaluation."""
    print("Preparing Subjective Evaluation")
    print("=" * 40)
    
    # Check required arguments
    if not args.model_path:
        print("Error: --model-path required for prepare mode")
        return
    
    if not os.path.exists(args.model_path):
        print(f"Error: Model not found at {args.model_path}")
        return
    
    if not os.path.exists(args.benchmark_dir):
        print(f"Error: Benchmark directory not found at {args.benchmark_dir}")
        print("Please create benchmark dataset first:")
        print("  python scripts/evaluate_model.py --create-benchmark")
        return
    
    # Set device
    device = args.device
    if device == 'cuda':
        import torch
        if not torch.cuda.is_available():
            print("CUDA not available, using CPU")
            device = 'cpu'
    
    print(f"Model: {args.model_path}")
    print(f"Benchmark dataset: {args.benchmark_dir}")
    print(f"Number of images: {args.num_images}")
    print(f"Device: {device}")
    print(f"Output directory: {args.output_dir}")
    
    try:
        # Prepare images
        pairs_file = prepare_subjective_evaluation(
            model_path=args.model_path,
            benchmark_dir=args.benchmark_dir,
            output_dir=args.output_dir,
            num_images=args.num_images,
            device=device
        )
        
        print(f"\nPreparation completed successfully!")
        print(f"Image pairs file: {pairs_file}")
        print(f"\nNext steps:")
        print(f"1. Run subjective evaluation:")
        print(f"   python scripts/subjective_evaluation.py --mode evaluate --image-pairs {pairs_file}")
        print(f"2. Collect evaluations from multiple evaluators")
        print(f"3. Analyze results:")
        print(f"   python scripts/subjective_evaluation.py --mode analyze --results-dir {args.output_dir}/evaluations")
        
    except Exception as e:
        print(f"Error during preparation: {e}")
        import traceback
        traceback.print_exc()


def evaluate_mode(args):
    """Run subjective evaluation interface."""
    print("Subjective Evaluation Interface")
    print("=" * 40)
    
    # Check required arguments
    if not args.image_pairs:
        # Try to find image pairs file in output directory
        pairs_file = os.path.join(args.output_dir, 'image_pairs.json')
        if os.path.exists(pairs_file):
            args.image_pairs = pairs_file
        else:
            print("Error: --image-pairs required for evaluate mode")
            print("Run prepare mode first to generate image pairs")
            return
    
    if not os.path.exists(args.image_pairs):
        print(f"Error: Image pairs file not found at {args.image_pairs}")
        return
    
    # Load image pairs
    try:
        with open(args.image_pairs, 'r') as f:
            image_pairs = json.load(f)
        
        print(f"Loaded {len(image_pairs)} image pairs")
        
    except Exception as e:
        print(f"Error loading image pairs: {e}")
        return
    
    # Generate evaluator ID if not provided
    if not args.evaluator_id:
        from datetime import datetime
        args.evaluator_id = f"evaluator_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # Create evaluations directory
    evaluations_dir = os.path.join(args.output_dir, 'evaluations')
    os.makedirs(evaluations_dir, exist_ok=True)
    
    # Output file for this evaluator
    output_file = os.path.join(evaluations_dir, f"{args.evaluator_id}.json")
    
    print(f"Evaluator ID: {args.evaluator_id}")
    print(f"Results will be saved to: {output_file}")
    print(f"\nStarting evaluation interface...")
    print(f"Instructions:")
    print(f"- Compare enhanced images with original images")
    print(f"- Rate quality improvement on scale 1-5")
    print(f"- Use arrow keys to navigate")
    print(f"- Press 1-5 for quick rating")
    print(f"- Click 'Save & Exit' when done")
    
    try:
        # Create and run evaluation interface
        interface = SubjectiveEvaluationInterface(
            image_pairs=image_pairs,
            output_file=output_file,
            evaluator_id=args.evaluator_id
        )
        
        interface.run()
        
        print(f"\nEvaluation completed!")
        print(f"Results saved to: {output_file}")
        
    except Exception as e:
        print(f"Error during evaluation: {e}")
        import traceback
        traceback.print_exc()


def analyze_mode(args):
    """Analyze subjective evaluation results."""
    print("Subjective Evaluation Analysis")
    print("=" * 40)
    
    # Determine results directory
    if not args.results_dir:
        args.results_dir = os.path.join(args.output_dir, 'evaluations')
    
    if not os.path.exists(args.results_dir):
        print(f"Error: Results directory not found at {args.results_dir}")
        print("Run evaluation mode first to collect subjective ratings")
        return
    
    # Check for evaluation files
    evaluation_files = [f for f in os.listdir(args.results_dir) if f.endswith('.json')]
    
    if not evaluation_files:
        print(f"Error: No evaluation files found in {args.results_dir}")
        return
    
    print(f"Found {len(evaluation_files)} evaluation files")
    print(f"Results directory: {args.results_dir}")
    
    try:
        # Create analyzer
        analyzer = MOSAnalyzer(args.results_dir)
        
        # Generate MOS report
        analysis_dir = os.path.join(args.output_dir, 'analysis')
        report_path = analyzer.generate_mos_report(analysis_dir)
        
        # Calculate and display MOS
        mos_results = analyzer.calculate_mos()
        
        if mos_results:
            print(f"\n" + "=" * 40)
            print("MEAN OPINION SCORE (MOS) RESULTS")
            print("=" * 40)
            print(f"Overall MOS: {mos_results['overall_mos']:.2f} ± {mos_results['overall_std']:.2f}")
            print(f"95% Confidence Interval: ±{mos_results['confidence_interval']:.2f}")
            print(f"Total Ratings: {mos_results['total_ratings']}")
            print(f"Number of Evaluators: {mos_results['num_evaluators']}")
            print(f"Number of Images: {mos_results['num_images']}")
            
            print(f"\nRating Distribution:")
            for rating in range(1, 6):
                count = mos_results['rating_distribution'][rating]
                percentage = mos_results['rating_percentages'][rating]
                print(f"  Rating {rating}: {count} ({percentage:.1f}%)")
            
            # Quality interpretation
            mos = mos_results['overall_mos']
            print(f"\nQuality Assessment:")
            if mos >= 4.5:
                print("  ✓ Excellent quality - Significant improvement")
            elif mos >= 4.0:
                print("  ✓ Very good quality - Clear improvement")
            elif mos >= 3.5:
                print("  ✓ Good quality - Noticeable improvement")
            elif mos >= 3.0:
                print("  ~ Fair quality - Similar to original")
            elif mos >= 2.5:
                print("  ✗ Poor quality - Slight degradation")
            else:
                print("  ✗ Very poor quality - Significant degradation")
            
            # Evaluator consistency
            evaluator_means = [stats['mean_rating'] for stats in mos_results['evaluator_stats'].values()]
            consistency = np.std(evaluator_means) if len(evaluator_means) > 1 else 0
            print(f"\nEvaluator Consistency: {consistency:.2f}")
            if consistency < 0.5:
                print("  ✓ High consistency among evaluators")
            elif consistency < 1.0:
                print("  ~ Moderate consistency among evaluators")
            else:
                print("  ✗ Low consistency - consider additional training")
            
            print(f"\nDetailed analysis saved to: {analysis_dir}")
            print("Files generated:")
            print(f"  - mos_report.txt: Comprehensive text report")
            print(f"  - mos_results.json: Detailed numerical results")
            print(f"  - rating_distribution.png: Rating distribution plot")
            if len(evaluation_files) > 1:
                print(f"  - evaluator_consistency.png: Evaluator comparison")
        
        print(f"\nAnalysis completed successfully!")
        
    except Exception as e:
        print(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    import numpy as np
    main()
