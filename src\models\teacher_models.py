"""Teacher models for knowledge distillation."""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple
import math


class ResidualBlock(nn.Module):
    """Residual block for super-resolution networks."""
    
    def __init__(self, channels: int, kernel_size: int = 3):
        """Initialize residual block.
        
        Args:
            channels: Number of channels
            kernel_size: Convolution kernel size
        """
        super().__init__()
        
        self.conv1 = nn.Conv2d(channels, channels, kernel_size, 
                              padding=kernel_size//2, bias=False)
        self.bn1 = nn.BatchNorm2d(channels)
        self.relu = nn.ReLU(inplace=True)
        
        self.conv2 = nn.Conv2d(channels, channels, kernel_size, 
                              padding=kernel_size//2, bias=False)
        self.bn2 = nn.BatchNorm2d(channels)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass."""
        residual = x
        
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        
        out = self.conv2(out)
        out = self.bn2(out)
        
        out += residual
        return out


class EDSR(nn.Module):
    """Enhanced Deep Residual Networks for Single Image Super-Resolution (EDSR)."""
    
    def __init__(self, 
                 in_channels: int = 3,
                 out_channels: int = 3,
                 num_features: int = 256,
                 num_blocks: int = 32,
                 upscale_factor: int = 1):
        """Initialize EDSR model.
        
        Args:
            in_channels: Number of input channels
            out_channels: Number of output channels
            num_features: Number of feature channels
            num_blocks: Number of residual blocks
            upscale_factor: Upscaling factor
        """
        super().__init__()
        
        self.upscale_factor = upscale_factor
        
        # Initial convolution
        self.conv_first = nn.Conv2d(in_channels, num_features, 3, padding=1)
        
        # Residual blocks
        self.res_blocks = nn.ModuleList([
            ResidualBlock(num_features) for _ in range(num_blocks)
        ])
        
        # Final convolution before upsampling
        self.conv_after_res = nn.Conv2d(num_features, num_features, 3, padding=1)
        
        # Upsampling layers
        if upscale_factor > 1:
            self.upsampler = self._make_upsampler(num_features, upscale_factor)
        else:
            self.upsampler = nn.Identity()
        
        # Final output convolution
        self.conv_last = nn.Conv2d(num_features, out_channels, 3, padding=1)
    
    def _make_upsampler(self, num_features: int, upscale_factor: int) -> nn.Module:
        """Create upsampling layers."""
        layers = []
        
        if upscale_factor == 2 or upscale_factor == 4:
            for _ in range(int(math.log2(upscale_factor))):
                layers.extend([
                    nn.Conv2d(num_features, num_features * 4, 3, padding=1),
                    nn.PixelShuffle(2)
                ])
        elif upscale_factor == 3:
            layers.extend([
                nn.Conv2d(num_features, num_features * 9, 3, padding=1),
                nn.PixelShuffle(3)
            ])
        else:
            # For other factors, use interpolation
            layers.append(nn.Upsample(scale_factor=upscale_factor, mode='bicubic'))
        
        return nn.Sequential(*layers)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass."""
        # Initial feature extraction
        feat = self.conv_first(x)
        
        # Residual blocks
        res = feat
        for block in self.res_blocks:
            res = block(res)
        
        # Add skip connection
        feat = feat + self.conv_after_res(res)
        
        # Upsampling
        feat = self.upsampler(feat)
        
        # Final output
        out = self.conv_last(feat)
        
        return out


class RRDBBlock(nn.Module):
    """Residual in Residual Dense Block (RRDB) for ESRGAN."""
    
    def __init__(self, num_features: int, num_grow_ch: int = 32):
        """Initialize RRDB block.
        
        Args:
            num_features: Number of feature channels
            num_grow_ch: Growth channels for dense connections
        """
        super().__init__()
        
        self.rdb1 = ResidualDenseBlock(num_features, num_grow_ch)
        self.rdb2 = ResidualDenseBlock(num_features, num_grow_ch)
        self.rdb3 = ResidualDenseBlock(num_features, num_grow_ch)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass."""
        out = self.rdb1(x)
        out = self.rdb2(out)
        out = self.rdb3(out)
        
        # Residual scaling
        return out * 0.2 + x


class ResidualDenseBlock(nn.Module):
    """Residual Dense Block."""
    
    def __init__(self, num_features: int, num_grow_ch: int = 32):
        """Initialize RDB.
        
        Args:
            num_features: Number of input features
            num_grow_ch: Growth channels
        """
        super().__init__()
        
        self.conv1 = nn.Conv2d(num_features, num_grow_ch, 3, padding=1)
        self.conv2 = nn.Conv2d(num_features + num_grow_ch, num_grow_ch, 3, padding=1)
        self.conv3 = nn.Conv2d(num_features + 2 * num_grow_ch, num_grow_ch, 3, padding=1)
        self.conv4 = nn.Conv2d(num_features + 3 * num_grow_ch, num_grow_ch, 3, padding=1)
        self.conv5 = nn.Conv2d(num_features + 4 * num_grow_ch, num_features, 3, padding=1)
        
        self.lrelu = nn.LeakyReLU(negative_slope=0.2, inplace=True)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass."""
        x1 = self.lrelu(self.conv1(x))
        x2 = self.lrelu(self.conv2(torch.cat((x, x1), 1)))
        x3 = self.lrelu(self.conv3(torch.cat((x, x1, x2), 1)))
        x4 = self.lrelu(self.conv4(torch.cat((x, x1, x2, x3), 1)))
        x5 = self.conv5(torch.cat((x, x1, x2, x3, x4), 1))
        
        # Residual scaling
        return x5 * 0.2 + x


class ESRGAN(nn.Module):
    """Enhanced Super-Resolution Generative Adversarial Networks (ESRGAN)."""
    
    def __init__(self, 
                 in_channels: int = 3,
                 out_channels: int = 3,
                 num_features: int = 64,
                 num_blocks: int = 23,
                 num_grow_ch: int = 32,
                 upscale_factor: int = 1):
        """Initialize ESRGAN model.
        
        Args:
            in_channels: Number of input channels
            out_channels: Number of output channels
            num_features: Number of feature channels
            num_blocks: Number of RRDB blocks
            num_grow_ch: Growth channels for dense connections
            upscale_factor: Upscaling factor
        """
        super().__init__()
        
        self.upscale_factor = upscale_factor
        
        # Initial convolution
        self.conv_first = nn.Conv2d(in_channels, num_features, 3, padding=1)
        
        # RRDB blocks
        self.rrdb_blocks = nn.ModuleList([
            RRDBBlock(num_features, num_grow_ch) for _ in range(num_blocks)
        ])
        
        # Convolution after RRDB blocks
        self.conv_after_rrdb = nn.Conv2d(num_features, num_features, 3, padding=1)
        
        # Upsampling
        if upscale_factor > 1:
            self.upsampler = self._make_upsampler(num_features, upscale_factor)
        else:
            self.upsampler = nn.Identity()
        
        # Final convolutions
        self.conv_hr = nn.Conv2d(num_features, num_features, 3, padding=1)
        self.conv_last = nn.Conv2d(num_features, out_channels, 3, padding=1)
        
        self.lrelu = nn.LeakyReLU(negative_slope=0.2, inplace=True)
    
    def _make_upsampler(self, num_features: int, upscale_factor: int) -> nn.Module:
        """Create upsampling layers."""
        layers = []
        
        if upscale_factor == 2 or upscale_factor == 4:
            for _ in range(int(math.log2(upscale_factor))):
                layers.extend([
                    nn.Conv2d(num_features, num_features * 4, 3, padding=1),
                    nn.PixelShuffle(2),
                    nn.LeakyReLU(negative_slope=0.2, inplace=True)
                ])
        elif upscale_factor == 3:
            layers.extend([
                nn.Conv2d(num_features, num_features * 9, 3, padding=1),
                nn.PixelShuffle(3),
                nn.LeakyReLU(negative_slope=0.2, inplace=True)
            ])
        else:
            layers.append(nn.Upsample(scale_factor=upscale_factor, mode='bicubic'))
        
        return nn.Sequential(*layers)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass."""
        # Initial feature extraction
        feat = self.conv_first(x)
        
        # RRDB blocks
        trunk = feat
        for block in self.rrdb_blocks:
            trunk = block(trunk)
        
        # Add skip connection
        feat = feat + self.conv_after_rrdb(trunk)
        
        # Upsampling
        feat = self.upsampler(feat)
        
        # Final output
        out = self.conv_last(self.lrelu(self.conv_hr(feat)))
        
        return out


class SRResNet(nn.Module):
    """Super-Resolution Residual Network."""
    
    def __init__(self, 
                 in_channels: int = 3,
                 out_channels: int = 3,
                 num_features: int = 64,
                 num_blocks: int = 16,
                 upscale_factor: int = 1):
        """Initialize SRResNet.
        
        Args:
            in_channels: Number of input channels
            out_channels: Number of output channels
            num_features: Number of feature channels
            num_blocks: Number of residual blocks
            upscale_factor: Upscaling factor
        """
        super().__init__()
        
        self.upscale_factor = upscale_factor
        
        # Initial convolution
        self.conv_first = nn.Conv2d(in_channels, num_features, 9, padding=4)
        self.relu = nn.ReLU(inplace=True)
        
        # Residual blocks
        self.res_blocks = nn.ModuleList([
            ResidualBlock(num_features) for _ in range(num_blocks)
        ])
        
        # Convolution after residual blocks
        self.conv_after_res = nn.Conv2d(num_features, num_features, 3, padding=1)
        self.bn_after_res = nn.BatchNorm2d(num_features)
        
        # Upsampling
        if upscale_factor > 1:
            self.upsampler = self._make_upsampler(num_features, upscale_factor)
        else:
            self.upsampler = nn.Identity()
        
        # Final convolution
        self.conv_last = nn.Conv2d(num_features, out_channels, 9, padding=4)
    
    def _make_upsampler(self, num_features: int, upscale_factor: int) -> nn.Module:
        """Create upsampling layers."""
        layers = []
        
        if upscale_factor == 2 or upscale_factor == 4:
            for _ in range(int(math.log2(upscale_factor))):
                layers.extend([
                    nn.Conv2d(num_features, num_features * 4, 3, padding=1),
                    nn.PixelShuffle(2),
                    nn.ReLU(inplace=True)
                ])
        elif upscale_factor == 3:
            layers.extend([
                nn.Conv2d(num_features, num_features * 9, 3, padding=1),
                nn.PixelShuffle(3),
                nn.ReLU(inplace=True)
            ])
        else:
            layers.append(nn.Upsample(scale_factor=upscale_factor, mode='bicubic'))
        
        return nn.Sequential(*layers)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass."""
        # Initial feature extraction
        feat = self.relu(self.conv_first(x))
        
        # Residual blocks
        res = feat
        for block in self.res_blocks:
            res = block(res)
        
        # Add skip connection
        feat = feat + self.bn_after_res(self.conv_after_res(res))
        
        # Upsampling
        feat = self.upsampler(feat)
        
        # Final output
        out = self.conv_last(feat)
        
        return out


class TeacherModelManager:
    """Manager for teacher models in knowledge distillation."""

    def __init__(self, model_name: str, device: str = 'cuda'):
        """Initialize teacher model manager.

        Args:
            model_name: Name of the teacher model
            device: Device to run the model on
        """
        self.model_name = model_name.lower()
        self.device = device
        self.model = None

        # Available teacher models
        self.available_models = {
            'edsr': EDSR,
            'esrgan': ESRGAN,
            'srresnet': SRResNet
        }

        if self.model_name not in self.available_models:
            raise ValueError(f"Unknown teacher model: {model_name}. "
                           f"Available models: {list(self.available_models.keys())}")

    def create_model(self, **kwargs) -> nn.Module:
        """Create teacher model instance.

        Args:
            **kwargs: Model-specific arguments

        Returns:
            Teacher model instance
        """
        model_class = self.available_models[self.model_name]

        # Default parameters for each model
        default_params = {
            'edsr': {
                'num_features': 256,
                'num_blocks': 32,
                'upscale_factor': 1
            },
            'esrgan': {
                'num_features': 64,
                'num_blocks': 23,
                'upscale_factor': 1
            },
            'srresnet': {
                'num_features': 64,
                'num_blocks': 16,
                'upscale_factor': 1
            }
        }

        # Merge default parameters with provided kwargs
        params = default_params[self.model_name].copy()
        params.update(kwargs)

        model = model_class(**params)
        model = model.to(self.device)

        return model

    def load_pretrained(self, checkpoint_path: str, **model_kwargs) -> nn.Module:
        """Load pretrained teacher model.

        Args:
            checkpoint_path: Path to model checkpoint
            **model_kwargs: Model creation arguments

        Returns:
            Loaded teacher model
        """
        import os

        # Create model
        self.model = self.create_model(**model_kwargs)

        # Load checkpoint
        if checkpoint_path and os.path.exists(checkpoint_path):
            print(f"Loading teacher model from {checkpoint_path}")
            checkpoint = torch.load(checkpoint_path, map_location=self.device)

            # Handle different checkpoint formats
            if 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            elif 'model' in checkpoint:
                state_dict = checkpoint['model']
            else:
                state_dict = checkpoint

            # Remove 'module.' prefix if present (from DataParallel)
            if any(key.startswith('module.') for key in state_dict.keys()):
                state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}

            self.model.load_state_dict(state_dict, strict=False)
            print("Teacher model loaded successfully")
        else:
            print(f"Warning: Checkpoint not found at {checkpoint_path}. Using randomly initialized model.")

        # Set to evaluation mode
        self.model.eval()

        return self.model

    def get_teacher_outputs(self, inputs: torch.Tensor) -> torch.Tensor:
        """Get teacher model outputs for knowledge distillation.

        Args:
            inputs: Input tensor batch

        Returns:
            Teacher model outputs
        """
        if self.model is None:
            raise ValueError("Teacher model not loaded. Call load_pretrained() first.")

        with torch.no_grad():
            outputs = self.model(inputs)

        return outputs

    def get_intermediate_features(self, inputs: torch.Tensor) -> dict:
        """Get intermediate features from teacher model.

        Args:
            inputs: Input tensor batch

        Returns:
            Dictionary of intermediate features
        """
        if self.model is None:
            raise ValueError("Teacher model not loaded. Call load_pretrained() first.")

        features = {}

        def hook_fn(name):
            def hook(module, input, output):
                features[name] = output.detach()
            return hook

        # Register hooks for intermediate layers
        hooks = []
        if self.model_name == 'edsr':
            # Hook after first conv and after residual blocks
            hooks.append(self.model.conv_first.register_forward_hook(hook_fn('conv_first')))
            hooks.append(self.model.conv_after_res.register_forward_hook(hook_fn('after_res')))
        elif self.model_name == 'esrgan':
            hooks.append(self.model.conv_first.register_forward_hook(hook_fn('conv_first')))
            hooks.append(self.model.conv_after_rrdb.register_forward_hook(hook_fn('after_rrdb')))
        elif self.model_name == 'srresnet':
            hooks.append(self.model.conv_first.register_forward_hook(hook_fn('conv_first')))
            hooks.append(self.model.conv_after_res.register_forward_hook(hook_fn('after_res')))

        # Forward pass
        with torch.no_grad():
            output = self.model(inputs)

        # Remove hooks
        for hook in hooks:
            hook.remove()

        features['output'] = output

        return features
