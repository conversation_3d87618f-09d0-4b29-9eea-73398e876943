"""Subjective evaluation framework for Mean Opinion Score (MOS) studies."""

import os
import json
import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import random
from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns


class SubjectiveEvaluationInterface:
    """GUI interface for subjective image quality evaluation."""
    
    def __init__(self, 
                 image_pairs: List[Tuple[str, str, str]],
                 output_file: str,
                 evaluator_id: str = None):
        """Initialize subjective evaluation interface.
        
        Args:
            image_pairs: List of (original, enhanced, reference) image paths
            output_file: Path to save evaluation results
            evaluator_id: Unique identifier for evaluator
        """
        self.image_pairs = image_pairs
        self.output_file = output_file
        self.evaluator_id = evaluator_id or f"evaluator_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Randomize order to avoid bias
        random.shuffle(self.image_pairs)
        
        # Evaluation state
        self.current_index = 0
        self.ratings = []
        self.start_time = datetime.now()
        
        # Rating scale
        self.rating_scale = {
            1: "Poor - Much worse than original",
            2: "Fair - Worse than original", 
            3: "Good - Similar to original",
            4: "Very Good - Better than original",
            5: "Excellent - Much better than original"
        }
        
        # Setup GUI
        self.setup_gui()
        
        # Load first image pair
        self.load_current_pair()
    
    def setup_gui(self):
        """Setup the GUI interface."""
        self.root = tk.Tk()
        self.root.title("Image Quality Evaluation")
        self.root.geometry("1400x800")
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Image Sharpening Quality Evaluation", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Progress info
        self.progress_label = ttk.Label(main_frame, text="", font=("Arial", 12))
        self.progress_label.grid(row=1, column=0, columnspan=3, pady=(0, 10))
        
        # Image display frame
        image_frame = ttk.Frame(main_frame)
        image_frame.grid(row=2, column=0, columnspan=3, pady=(0, 20), sticky=(tk.W, tk.E))
        
        # Original image
        ttk.Label(image_frame, text="Original", font=("Arial", 12, "bold")).grid(row=0, column=0, padx=10)
        self.original_label = ttk.Label(image_frame)
        self.original_label.grid(row=1, column=0, padx=10)
        
        # Enhanced image
        ttk.Label(image_frame, text="Enhanced", font=("Arial", 12, "bold")).grid(row=0, column=1, padx=10)
        self.enhanced_label = ttk.Label(image_frame)
        self.enhanced_label.grid(row=1, column=1, padx=10)
        
        # Reference image (if available)
        ttk.Label(image_frame, text="Reference", font=("Arial", 12, "bold")).grid(row=0, column=2, padx=10)
        self.reference_label = ttk.Label(image_frame)
        self.reference_label.grid(row=1, column=2, padx=10)
        
        # Rating frame
        rating_frame = ttk.LabelFrame(main_frame, text="Rate the Enhanced Image Quality", padding="10")
        rating_frame.grid(row=3, column=0, columnspan=3, pady=(0, 20), sticky=(tk.W, tk.E))
        
        # Rating scale
        self.rating_var = tk.IntVar()
        
        for value, description in self.rating_scale.items():
            ttk.Radiobutton(rating_frame, text=f"{value}: {description}", 
                           variable=self.rating_var, value=value).grid(row=value-1, column=0, sticky=tk.W, pady=2)
        
        # Instructions
        instructions = """
Instructions:
1. Compare the enhanced image with the original image
2. Rate the quality improvement on a scale of 1-5
3. Consider sharpness, clarity, and overall visual quality
4. Use the reference image (if available) as a quality target
        """
        
        instruction_label = ttk.Label(main_frame, text=instructions, font=("Arial", 10), 
                                    justify=tk.LEFT, foreground="gray")
        instruction_label.grid(row=4, column=0, columnspan=3, pady=(0, 20), sticky=tk.W)
        
        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=3, pady=(0, 10))
        
        ttk.Button(button_frame, text="Previous", command=self.previous_image).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="Next", command=self.next_image).grid(row=0, column=1, padx=5)
        ttk.Button(button_frame, text="Save & Exit", command=self.save_and_exit).grid(row=0, column=2, padx=5)
        
        # Bind keyboard shortcuts
        self.root.bind('<Left>', lambda e: self.previous_image())
        self.root.bind('<Right>', lambda e: self.next_image())
        self.root.bind('<Return>', lambda e: self.next_image())
        self.root.bind('<Key-1>', lambda e: self.set_rating(1))
        self.root.bind('<Key-2>', lambda e: self.set_rating(2))
        self.root.bind('<Key-3>', lambda e: self.set_rating(3))
        self.root.bind('<Key-4>', lambda e: self.set_rating(4))
        self.root.bind('<Key-5>', lambda e: self.set_rating(5))
        
        # Status bar
        self.status_label = ttk.Label(main_frame, text="Use arrow keys or buttons to navigate. Press 1-5 for quick rating.", 
                                     font=("Arial", 9), foreground="gray")
        self.status_label.grid(row=6, column=0, columnspan=3, pady=(10, 0))
    
    def load_current_pair(self):
        """Load current image pair for evaluation."""
        if self.current_index >= len(self.image_pairs):
            self.finish_evaluation()
            return
        
        original_path, enhanced_path, reference_path = self.image_pairs[self.current_index]
        
        # Update progress
        progress_text = f"Image {self.current_index + 1} of {len(self.image_pairs)}"
        self.progress_label.config(text=progress_text)
        
        # Load and display images
        try:
            # Original image
            original_img = self.load_and_resize_image(original_path, max_size=400)
            self.original_label.config(image=original_img)
            self.original_label.image = original_img  # Keep reference
            
            # Enhanced image
            enhanced_img = self.load_and_resize_image(enhanced_path, max_size=400)
            self.enhanced_label.config(image=enhanced_img)
            self.enhanced_label.image = enhanced_img  # Keep reference
            
            # Reference image (if available)
            if reference_path and os.path.exists(reference_path):
                reference_img = self.load_and_resize_image(reference_path, max_size=400)
                self.reference_label.config(image=reference_img)
                self.reference_label.image = reference_img  # Keep reference
            else:
                self.reference_label.config(image="")
                self.reference_label.image = None
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load images: {e}")
        
        # Load previous rating if available
        if self.current_index < len(self.ratings):
            self.rating_var.set(self.ratings[self.current_index]['rating'])
        else:
            self.rating_var.set(0)
    
    def load_and_resize_image(self, image_path: str, max_size: int = 400) -> ImageTk.PhotoImage:
        """Load and resize image for display.
        
        Args:
            image_path: Path to image file
            max_size: Maximum size for display
            
        Returns:
            PhotoImage for tkinter
        """
        # Load image
        image = Image.open(image_path)
        
        # Resize while maintaining aspect ratio
        image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
        
        # Convert to PhotoImage
        return ImageTk.PhotoImage(image)
    
    def set_rating(self, rating: int):
        """Set rating for current image."""
        self.rating_var.set(rating)
    
    def save_current_rating(self):
        """Save rating for current image."""
        rating = self.rating_var.get()
        
        if rating == 0:
            messagebox.showwarning("Warning", "Please select a rating before proceeding.")
            return False
        
        # Save or update rating
        rating_data = {
            'image_index': self.current_index,
            'original_path': self.image_pairs[self.current_index][0],
            'enhanced_path': self.image_pairs[self.current_index][1],
            'reference_path': self.image_pairs[self.current_index][2],
            'rating': rating,
            'timestamp': datetime.now().isoformat()
        }
        
        # Update ratings list
        if self.current_index < len(self.ratings):
            self.ratings[self.current_index] = rating_data
        else:
            self.ratings.append(rating_data)
        
        return True
    
    def next_image(self):
        """Move to next image."""
        if not self.save_current_rating():
            return
        
        self.current_index += 1
        self.load_current_pair()
    
    def previous_image(self):
        """Move to previous image."""
        if self.current_index > 0:
            self.save_current_rating()  # Save current rating
            self.current_index -= 1
            self.load_current_pair()
    
    def save_and_exit(self):
        """Save results and exit."""
        if self.current_index < len(self.image_pairs):
            if not self.save_current_rating():
                return
        
        self.save_results()
        self.root.quit()
    
    def finish_evaluation(self):
        """Finish evaluation when all images are rated."""
        messagebox.showinfo("Complete", "Evaluation completed! Thank you for your participation.")
        self.save_results()
        self.root.quit()
    
    def save_results(self):
        """Save evaluation results to file."""
        results = {
            'evaluator_id': self.evaluator_id,
            'start_time': self.start_time.isoformat(),
            'end_time': datetime.now().isoformat(),
            'total_images': len(self.image_pairs),
            'completed_images': len(self.ratings),
            'rating_scale': self.rating_scale,
            'ratings': self.ratings
        }
        
        # Save to JSON file
        os.makedirs(os.path.dirname(self.output_file), exist_ok=True)
        with open(self.output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"Results saved to: {self.output_file}")
    
    def run(self):
        """Run the evaluation interface."""
        self.root.mainloop()


class MOSAnalyzer:
    """Analyzer for Mean Opinion Score (MOS) studies."""
    
    def __init__(self, results_dir: str):
        """Initialize MOS analyzer.
        
        Args:
            results_dir: Directory containing evaluation results
        """
        self.results_dir = results_dir
        self.all_results = self.load_all_results()
    
    def load_all_results(self) -> List[Dict]:
        """Load all evaluation results from directory.
        
        Returns:
            List of evaluation results
        """
        results = []
        
        for filename in os.listdir(self.results_dir):
            if filename.endswith('.json'):
                filepath = os.path.join(self.results_dir, filename)
                try:
                    with open(filepath, 'r') as f:
                        result = json.load(f)
                        results.append(result)
                except Exception as e:
                    print(f"Error loading {filepath}: {e}")
        
        print(f"Loaded {len(results)} evaluation results")
        return results
    
    def calculate_mos(self) -> Dict:
        """Calculate Mean Opinion Score and statistics.
        
        Returns:
            MOS analysis results
        """
        if not self.all_results:
            return {}
        
        # Collect all ratings
        all_ratings = []
        evaluator_ratings = {}
        image_ratings = {}
        
        for result in self.all_results:
            evaluator_id = result['evaluator_id']
            evaluator_ratings[evaluator_id] = []
            
            for rating_data in result['ratings']:
                rating = rating_data['rating']
                image_path = rating_data['enhanced_path']
                
                all_ratings.append(rating)
                evaluator_ratings[evaluator_id].append(rating)
                
                if image_path not in image_ratings:
                    image_ratings[image_path] = []
                image_ratings[image_path].append(rating)
        
        if not all_ratings:
            return {}
        
        # Calculate overall MOS
        overall_mos = np.mean(all_ratings)
        overall_std = np.std(all_ratings)
        
        # Calculate per-evaluator statistics
        evaluator_stats = {}
        for evaluator_id, ratings in evaluator_ratings.items():
            evaluator_stats[evaluator_id] = {
                'mean_rating': np.mean(ratings),
                'std_rating': np.std(ratings),
                'num_ratings': len(ratings)
            }
        
        # Calculate per-image statistics
        image_stats = {}
        for image_path, ratings in image_ratings.items():
            image_stats[image_path] = {
                'mean_rating': np.mean(ratings),
                'std_rating': np.std(ratings),
                'num_ratings': len(ratings)
            }
        
        # Calculate rating distribution
        rating_counts = {i: all_ratings.count(i) for i in range(1, 6)}
        rating_percentages = {i: (count / len(all_ratings)) * 100 for i, count in rating_counts.items()}
        
        # Calculate confidence interval (95%)
        confidence_interval = 1.96 * (overall_std / np.sqrt(len(all_ratings)))
        
        results = {
            'overall_mos': overall_mos,
            'overall_std': overall_std,
            'confidence_interval': confidence_interval,
            'total_ratings': len(all_ratings),
            'num_evaluators': len(evaluator_ratings),
            'num_images': len(image_ratings),
            'rating_distribution': rating_counts,
            'rating_percentages': rating_percentages,
            'evaluator_stats': evaluator_stats,
            'image_stats': image_stats
        }
        
        return results
    
    def generate_mos_report(self, output_dir: str) -> str:
        """Generate comprehensive MOS report.
        
        Args:
            output_dir: Output directory for report
            
        Returns:
            Path to generated report
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # Calculate MOS
        mos_results = self.calculate_mos()
        
        if not mos_results:
            print("No valid results found for MOS analysis")
            return ""
        
        # Generate text report
        report_path = os.path.join(output_dir, 'mos_report.txt')
        
        with open(report_path, 'w') as f:
            f.write("Mean Opinion Score (MOS) Analysis Report\n")
            f.write("=" * 50 + "\n\n")
            
            # Overall results
            f.write("Overall Results:\n")
            f.write("-" * 20 + "\n")
            f.write(f"Mean Opinion Score (MOS): {mos_results['overall_mos']:.2f}\n")
            f.write(f"Standard Deviation: {mos_results['overall_std']:.2f}\n")
            f.write(f"95% Confidence Interval: ±{mos_results['confidence_interval']:.2f}\n")
            f.write(f"Total Ratings: {mos_results['total_ratings']}\n")
            f.write(f"Number of Evaluators: {mos_results['num_evaluators']}\n")
            f.write(f"Number of Images: {mos_results['num_images']}\n\n")
            
            # Rating distribution
            f.write("Rating Distribution:\n")
            f.write("-" * 20 + "\n")
            for rating in range(1, 6):
                count = mos_results['rating_distribution'][rating]
                percentage = mos_results['rating_percentages'][rating]
                f.write(f"Rating {rating}: {count} ({percentage:.1f}%)\n")
            f.write("\n")
            
            # Quality interpretation
            f.write("Quality Interpretation:\n")
            f.write("-" * 20 + "\n")
            mos = mos_results['overall_mos']
            if mos >= 4.5:
                f.write("Excellent quality - Significant improvement over original\n")
            elif mos >= 4.0:
                f.write("Very good quality - Clear improvement over original\n")
            elif mos >= 3.5:
                f.write("Good quality - Noticeable improvement over original\n")
            elif mos >= 3.0:
                f.write("Fair quality - Similar to original\n")
            elif mos >= 2.5:
                f.write("Poor quality - Slight degradation from original\n")
            else:
                f.write("Very poor quality - Significant degradation from original\n")
            f.write("\n")
            
            # Evaluator consistency
            f.write("Evaluator Statistics:\n")
            f.write("-" * 20 + "\n")
            evaluator_means = [stats['mean_rating'] for stats in mos_results['evaluator_stats'].values()]
            evaluator_consistency = np.std(evaluator_means)
            f.write(f"Inter-evaluator consistency (std of means): {evaluator_consistency:.2f}\n")
            
            if evaluator_consistency < 0.5:
                f.write("High consistency among evaluators\n")
            elif evaluator_consistency < 1.0:
                f.write("Moderate consistency among evaluators\n")
            else:
                f.write("Low consistency among evaluators - consider additional training\n")
        
        # Save detailed results as JSON
        json_path = os.path.join(output_dir, 'mos_results.json')
        with open(json_path, 'w') as f:
            json.dump(mos_results, f, indent=2)
        
        # Generate visualizations
        self._generate_mos_visualizations(mos_results, output_dir)
        
        print(f"MOS report saved to: {report_path}")
        return report_path
    
    def _generate_mos_visualizations(self, mos_results: Dict, output_dir: str):
        """Generate MOS visualization plots.
        
        Args:
            mos_results: MOS analysis results
            output_dir: Output directory
        """
        # Rating distribution
        plt.figure(figsize=(10, 6))
        
        ratings = list(range(1, 6))
        counts = [mos_results['rating_distribution'][r] for r in ratings]
        percentages = [mos_results['rating_percentages'][r] for r in ratings]
        
        bars = plt.bar(ratings, counts, alpha=0.7, color='skyblue')
        
        # Add percentage labels on bars
        for bar, percentage in zip(bars, percentages):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{percentage:.1f}%', ha='center', va='bottom')
        
        plt.xlabel('Rating')
        plt.ylabel('Number of Ratings')
        plt.title('Rating Distribution')
        plt.xticks(ratings)
        plt.grid(axis='y', alpha=0.3)
        
        # Add MOS line
        mos = mos_results['overall_mos']
        plt.axvline(x=mos, color='red', linestyle='--', linewidth=2, 
                   label=f'MOS = {mos:.2f}')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'rating_distribution.png'), dpi=300)
        plt.close()
        
        # Evaluator comparison
        if len(mos_results['evaluator_stats']) > 1:
            plt.figure(figsize=(12, 6))
            
            evaluators = list(mos_results['evaluator_stats'].keys())
            means = [mos_results['evaluator_stats'][e]['mean_rating'] for e in evaluators]
            stds = [mos_results['evaluator_stats'][e]['std_rating'] for e in evaluators]
            
            x_pos = np.arange(len(evaluators))
            plt.bar(x_pos, means, yerr=stds, capsize=5, alpha=0.7)
            
            plt.axhline(y=mos_results['overall_mos'], color='red', linestyle='--', 
                       label=f'Overall MOS = {mos_results["overall_mos"]:.2f}')
            
            plt.xlabel('Evaluator')
            plt.ylabel('Mean Rating')
            plt.title('Rating Consistency Across Evaluators')
            plt.xticks(x_pos, [f'E{i+1}' for i in range(len(evaluators))], rotation=45)
            plt.legend()
            plt.grid(axis='y', alpha=0.3)
            
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'evaluator_consistency.png'), dpi=300)
            plt.close()
        
        print(f"MOS visualizations saved to: {output_dir}")


def prepare_subjective_evaluation(model_path: str,
                                benchmark_dir: str,
                                output_dir: str,
                                num_images: int = 50,
                                device: str = 'cuda') -> str:
    """Prepare images for subjective evaluation.
    
    Args:
        model_path: Path to trained model
        benchmark_dir: Benchmark dataset directory
        output_dir: Output directory for prepared images
        num_images: Number of images to prepare
        device: Device for inference
        
    Returns:
        Path to image pairs file
    """
    from ..inference.optimized_inference import OptimizedInferenceEngine
    
    print(f"Preparing {num_images} images for subjective evaluation...")
    
    # Initialize inference engine
    engine = OptimizedInferenceEngine(model_path, device)
    
    # Create output directories
    os.makedirs(output_dir, exist_ok=True)
    original_dir = os.path.join(output_dir, 'original')
    enhanced_dir = os.path.join(output_dir, 'enhanced')
    degraded_dir = os.path.join(output_dir, 'degraded')
    
    for dir_path in [original_dir, enhanced_dir, degraded_dir]:
        os.makedirs(dir_path, exist_ok=True)
    
    # Collect images from benchmark dataset
    image_files = []
    categories = ['text', 'nature', 'people', 'animals', 'games']
    
    for category in categories:
        category_dir = os.path.join(benchmark_dir, category)
        if os.path.exists(category_dir):
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                category_files = [
                    os.path.join(category_dir, f)
                    for f in os.listdir(category_dir)
                    if f.lower().endswith(ext)
                ]
                image_files.extend(category_files)
    
    # Randomly select images
    selected_images = random.sample(image_files, min(num_images, len(image_files)))
    
    # Process images
    image_pairs = []
    
    for i, image_path in enumerate(selected_images):
        print(f"Processing image {i+1}/{len(selected_images)}")
        
        # Load original image
        original_image = cv2.imread(image_path)
        if original_image is None:
            continue
        
        # Apply degradation
        from ..evaluation.evaluator import ModelEvaluator
        evaluator = ModelEvaluator(model_path, device)
        degraded_image = evaluator._apply_test_degradation(original_image)
        
        # Enhance image
        enhanced_image, _ = engine.infer_image(degraded_image)
        
        # Save images
        base_name = f"image_{i+1:03d}.png"
        
        original_path = os.path.join(original_dir, base_name)
        degraded_path = os.path.join(degraded_dir, base_name)
        enhanced_path = os.path.join(enhanced_dir, base_name)
        
        cv2.imwrite(original_path, original_image)
        cv2.imwrite(degraded_path, degraded_image)
        cv2.imwrite(enhanced_path, enhanced_image)
        
        # Store pair info (degraded as input, enhanced as output, original as reference)
        image_pairs.append((degraded_path, enhanced_path, original_path))
    
    # Save image pairs list
    pairs_file = os.path.join(output_dir, 'image_pairs.json')
    with open(pairs_file, 'w') as f:
        json.dump(image_pairs, f, indent=2)
    
    print(f"Prepared {len(image_pairs)} image pairs for subjective evaluation")
    print(f"Image pairs saved to: {pairs_file}")
    
    return pairs_file
