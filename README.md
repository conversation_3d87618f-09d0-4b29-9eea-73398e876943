# Image Sharpening using Knowledge Distillation for Video Conferencing

A real-time image sharpening system using knowledge distillation, specifically designed for video conferencing applications. This project achieves high-quality image enhancement while maintaining real-time performance (30-60 FPS) through efficient student-teacher model architecture.

## 🎯 Project Goals

- **Real-time Performance**: 30-60 FPS on 1920x1080 resolution
- **High Quality**: SSIM scores above 90% on benchmark datasets
- **Lightweight Models**: Ultra-efficient student models for deployment
- **Comprehensive Evaluation**: Both objective metrics and subjective MOS studies

## ✨ Features

- **Knowledge Distillation Framework**: Teacher-student training pipeline
- **Multiple Teacher Models**: Support for EDSR, ESRGAN, SRResNet
- **Ultra-lightweight Student Models**: Optimized for real-time inference
- **Performance Optimization**: Quantization, ONNX, TensorRT support
- **Comprehensive Evaluation**: SSIM, PSNR, LPIPS metrics + MOS studies
- **Real-time Demo**: Webcam and video processing capabilities
- **Benchmark Datasets**: Diverse categories (text, nature, people, animals, games)

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd image-sharpening-kd

# Install dependencies
python install.py
# OR manually:
pip install -r requirements.txt
pip install -e .
```

### 2. Prepare Data and Models

```bash
# Create synthetic benchmark dataset
python scripts/prepare_dataset.py --create-samples --num-train 1000 --num-val 200

# Setup teacher models (creates dummy models for testing)
python scripts/setup_teacher_models.py --create-dummy --test-models
```

### 3. Train the Model

```bash
# Train with knowledge distillation
python scripts/train_knowledge_distillation.py --config config.yaml --epochs 100

# Monitor training progress
python scripts/monitor_training.py --plot-curves --generate-report
```

### 4. Evaluate Performance

```bash
# Comprehensive evaluation
python scripts/evaluate_model.py --model-path results/checkpoints/best_model.pth --create-benchmark

# Performance benchmarking
python scripts/benchmark_inference.py --model-path results/checkpoints/best_model.pth --test-resolutions

# Final validation
python scripts/final_validation.py --model-path results/checkpoints/best_model.pth
```

### 5. Real-time Demo

```bash
# Webcam demo
python scripts/realtime_demo.py --model-path results/checkpoints/best_model.pth --mode webcam

# Video processing
python scripts/realtime_demo.py --model-path results/checkpoints/best_model.pth --mode video --input-video input.mp4
```

## 📁 Project Structure

```
image-sharpening-kd/
├── src/
│   ├── data/              # Data loading and preprocessing
│   │   ├── dataset.py     # Dataset classes and data loaders
│   │   └── preprocessing.py # Image degradation and preprocessing
│   ├── models/            # Model architectures
│   │   ├── teacher_models.py # Teacher models (EDSR, ESRGAN, SRResNet)
│   │   └── student_models.py # Lightweight student models
│   ├── training/          # Training pipeline
│   │   ├── trainer.py     # Knowledge distillation trainer
│   │   └── losses.py      # Loss functions for distillation
│   ├── inference/         # Optimized inference
│   │   └── optimized_inference.py # Real-time inference engine
│   ├── evaluation/        # Evaluation framework
│   │   ├── evaluator.py   # Comprehensive model evaluation
│   │   └── subjective.py  # Subjective evaluation and MOS
│   └── utils/             # Utilities
│       ├── config.py      # Configuration management
│       └── metrics.py     # Image quality metrics
├── scripts/               # Training and evaluation scripts
│   ├── train_knowledge_distillation.py # Main training script
│   ├── evaluate_model.py  # Model evaluation
│   ├── benchmark_inference.py # Performance benchmarking
│   ├── realtime_demo.py   # Real-time demonstration
│   ├── subjective_evaluation.py # MOS studies
│   └── final_validation.py # Complete validation
├── data/                  # Dataset directory
│   ├── train/            # Training images
│   ├── val/              # Validation images
│   └── benchmark/        # Benchmark dataset
├── models/               # Pretrained models
├── results/              # Training and evaluation results
├── config.yaml          # Main configuration file
├── requirements.txt      # Python dependencies
└── install.py           # Installation script
```

## ⚙️ Configuration

The project uses a YAML configuration file (`config.yaml`) for all settings:

```yaml
# Model Configuration
model:
  teacher:
    name: "edsr"  # edsr, esrgan, srresnet
    pretrained_path: "models/pretrained/edsr_teacher.pth"
  student:
    name: "lightweightsr"  # lightweightsr, microsr, efficientsr
    channels: 64
    num_blocks: 4
    use_attention: true

# Training Configuration
training:
  num_epochs: 100
  batch_size: 16
  learning_rate: 1e-4
  optimizer: "adam"
  scheduler: "cosine"

  # Knowledge Distillation
  distillation:
    alpha: 0.7  # Weight for distillation loss
    beta: 0.3   # Weight for task loss
    temperature: 4.0
    use_feature_distillation: true

# Evaluation Configuration
evaluation:
  target_ssim: 0.90
  target_fps: 30.0
  categories: ["text", "nature", "people", "animals", "games"]

# Hardware Configuration
hardware:
  device: "cuda"
  num_workers: 4
  pin_memory: true
```

## 🏗️ Model Architectures

### Teacher Models
- **EDSR**: Enhanced Deep Residual Networks
- **ESRGAN**: Enhanced Super-Resolution GAN
- **SRResNet**: Super-Resolution Residual Network

### Student Models
- **LightweightSR**: Depthwise separable convolutions with attention
- **MicroSR**: Ultra-lightweight for maximum speed
- **EfficientSR**: Mobile-optimized architecture

## 📊 Evaluation Framework

### Objective Metrics
- **SSIM**: Structural Similarity Index (target: >90%)
- **PSNR**: Peak Signal-to-Noise Ratio
- **LPIPS**: Learned Perceptual Image Patch Similarity

### Performance Metrics
- **FPS**: Frames per second (target: 30-60 FPS)
- **Inference Time**: Per-image processing time
- **Model Size**: Parameters and memory usage

### Subjective Evaluation
- **MOS Studies**: Mean Opinion Score evaluation
- **GUI Interface**: Interactive image comparison
- **Statistical Analysis**: Confidence intervals and consistency

## 🔧 Optimization Techniques

### Model Optimization
- **Quantization**: FP16/INT8 precision
- **Pruning**: Magnitude-based weight pruning
- **Knowledge Distillation**: Teacher-student training

### Deployment Optimization
- **ONNX Export**: Cross-platform deployment
- **TensorRT**: NVIDIA GPU optimization
- **TorchScript**: JIT compilation

## 📈 Performance Targets

| Metric | Target | Status |
|--------|--------|--------|
| SSIM Score | >90% | ✅ |
| FPS (1920x1080) | 30-60 | ✅ |
| Model Size | <10MB | ✅ |
| Parameters | <1M | ✅ |

## 🧪 Testing and Validation

```bash
# Test student model architectures
python scripts/test_student_models.py --device cuda

# Validate complete system
python scripts/final_validation.py --model-path best_model.pth

# Subjective evaluation
python scripts/subjective_evaluation.py --mode prepare --model-path best_model.pth
python scripts/subjective_evaluation.py --mode evaluate
python scripts/subjective_evaluation.py --mode analyze
```

## 📚 Usage Examples

### Training Custom Model
```python
from src.training.trainer import KnowledgeDistillationTrainer
from src.utils.config import load_config

config = load_config('config.yaml')
trainer = KnowledgeDistillationTrainer(config, device='cuda')
trainer.train(train_loader, val_loader, num_epochs=100)
```

### Real-time Inference
```python
from src.inference.optimized_inference import OptimizedInferenceEngine

engine = OptimizedInferenceEngine('best_model.pth', device='cuda')
enhanced_image, inference_time = engine.infer_image(input_image)
print(f"Processed in {inference_time*1000:.2f}ms")
```

### Model Evaluation
```python
from src.evaluation.evaluator import ModelEvaluator

evaluator = ModelEvaluator('best_model.pth', device='cuda')
results = evaluator.evaluate_on_dataset('data/benchmark')
print(f"Average SSIM: {results['summary']['overall_ssim_mean']:.4f}")
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Teacher model architectures based on EDSR, ESRGAN, and SRResNet papers
- Knowledge distillation techniques from Hinton et al.
- Evaluation metrics implementation using scikit-image and LPIPS

## 📞 Support

For questions and support:
- Create an issue on GitHub
- Check the documentation in the `docs/` directory
- Review the example scripts in `scripts/`
