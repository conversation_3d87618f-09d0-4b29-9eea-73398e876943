"""Final validation script for the complete image sharpening system."""

import os
import argparse
import torch
import json
import time
from pathlib import Path
import sys

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.evaluation.evaluator import ModelEvaluator, create_benchmark_dataset
from src.inference.optimized_inference import OptimizedInferenceEngine
from src.utils.config import load_config


def validate_model_requirements(model_path: str, 
                               benchmark_dir: str,
                               device: str = 'cuda',
                               target_ssim: float = 0.90,
                               target_fps: float = 30.0) -> dict:
    """Validate model against all project requirements.
    
    Args:
        model_path: Path to trained model
        benchmark_dir: Benchmark dataset directory
        device: Device for evaluation
        target_ssim: Target SSIM score
        target_fps: Target FPS performance
        
    Returns:
        Validation results
    """
    print("Final Model Validation")
    print("=" * 50)
    print(f"Model: {model_path}")
    print(f"Target SSIM: {target_ssim}")
    print(f"Target FPS: {target_fps}")
    print(f"Device: {device}")
    
    results = {
        'model_path': model_path,
        'target_ssim': target_ssim,
        'target_fps': target_fps,
        'device': device,
        'validation_results': {},
        'requirements_met': {},
        'overall_pass': False
    }
    
    try:
        # 1. Model Loading Test
        print(f"\n1. Testing Model Loading...")
        print("-" * 30)
        
        try:
            engine = OptimizedInferenceEngine(model_path, device)
            print("✓ Model loaded successfully")
            results['validation_results']['model_loading'] = True
        except Exception as e:
            print(f"✗ Model loading failed: {e}")
            results['validation_results']['model_loading'] = False
            return results
        
        # 2. Performance Benchmarking
        print(f"\n2. Performance Benchmarking...")
        print("-" * 30)
        
        # Test different resolutions
        resolutions = [
            (256, 256),    # Training size
            (720, 1280),   # HD
            (1080, 1920),  # Full HD (target)
        ]
        
        performance_results = {}
        
        for height, width in resolutions:
            print(f"Testing {width}x{height} resolution...")
            
            try:
                benchmark_result = engine.benchmark(
                    input_shape=(height, width),
                    num_runs=50
                )
                
                fps = benchmark_result['avg_fps']
                inference_time = benchmark_result['avg_inference_time'] * 1000  # ms
                
                performance_results[f"{width}x{height}"] = {
                    'fps': fps,
                    'inference_time_ms': inference_time,
                    'meets_target': fps >= target_fps
                }
                
                status = "✓" if fps >= target_fps else "✗"
                print(f"  {status} FPS: {fps:.1f} (target: {target_fps})")
                print(f"    Inference time: {inference_time:.2f}ms")
                
            except Exception as e:
                print(f"  ✗ Error: {e}")
                performance_results[f"{width}x{height}"] = {
                    'error': str(e),
                    'meets_target': False
                }
        
        results['validation_results']['performance'] = performance_results
        
        # Check if target resolution meets FPS requirement
        target_resolution = "1920x1080"
        if target_resolution in performance_results:
            target_fps_met = performance_results[target_resolution].get('meets_target', False)
            results['requirements_met']['fps_target'] = target_fps_met
        else:
            results['requirements_met']['fps_target'] = False
        
        # 3. Accuracy Evaluation
        print(f"\n3. Accuracy Evaluation...")
        print("-" * 30)
        
        # Check if benchmark dataset exists
        if not os.path.exists(benchmark_dir):
            print(f"Creating benchmark dataset at {benchmark_dir}...")
            create_benchmark_dataset(benchmark_dir)
        
        # Run evaluation
        evaluator = ModelEvaluator(model_path, device, target_ssim)
        
        eval_results = evaluator.evaluate_on_dataset(
            dataset_dir=benchmark_dir,
            categories=['text', 'nature', 'people', 'animals', 'games'],
            max_images_per_category=20  # Reduced for faster validation
        )
        
        if eval_results and 'summary' in eval_results:
            summary = eval_results['summary']
            
            avg_ssim = summary['overall_ssim_mean']
            ssim_target_met = summary['target_ssim_achieved']
            images_above_target = summary['percentage_above_target']
            
            print(f"Average SSIM: {avg_ssim:.4f}")
            print(f"Target SSIM ({target_ssim}): {'✓ Achieved' if ssim_target_met else '✗ Not achieved'}")
            print(f"Images above target: {images_above_target:.1f}%")
            
            results['validation_results']['accuracy'] = {
                'avg_ssim': avg_ssim,
                'target_ssim_met': ssim_target_met,
                'percentage_above_target': images_above_target,
                'total_images': summary['total_images']
            }
            
            results['requirements_met']['ssim_target'] = ssim_target_met
            
            # Category breakdown
            category_results = {}
            for category, cat_data in eval_results['by_category'].items():
                category_ssim = cat_data['ssim_mean']
                category_results[category] = {
                    'ssim': category_ssim,
                    'meets_target': category_ssim >= target_ssim
                }
                
                status = "✓" if category_ssim >= target_ssim else "✗"
                print(f"  {status} {category}: SSIM {category_ssim:.4f}")
            
            results['validation_results']['category_performance'] = category_results
        
        else:
            print("✗ Accuracy evaluation failed")
            results['validation_results']['accuracy'] = {'error': 'Evaluation failed'}
            results['requirements_met']['ssim_target'] = False
        
        # 4. Model Size and Efficiency
        print(f"\n4. Model Efficiency Analysis...")
        print("-" * 30)
        
        try:
            # Load model to check parameters
            if model_path.endswith('.pth'):
                checkpoint = torch.load(model_path, map_location='cpu')
                
                if 'student_model_state_dict' in checkpoint:
                    state_dict = checkpoint['student_model_state_dict']
                elif 'model_state_dict' in checkpoint:
                    state_dict = checkpoint['model_state_dict']
                else:
                    state_dict = checkpoint
                
                # Count parameters
                total_params = sum(p.numel() for p in state_dict.values())
                model_size_mb = sum(p.numel() * p.element_size() for p in state_dict.values()) / (1024 * 1024)
                
                print(f"Model parameters: {total_params:,}")
                print(f"Model size: {model_size_mb:.2f} MB")
                
                # Efficiency criteria (lightweight model)
                is_lightweight = total_params < 1_000_000  # Less than 1M parameters
                is_compact = model_size_mb < 10  # Less than 10MB
                
                print(f"Lightweight (< 1M params): {'✓' if is_lightweight else '✗'}")
                print(f"Compact (< 10MB): {'✓' if is_compact else '✗'}")
                
                results['validation_results']['efficiency'] = {
                    'total_parameters': total_params,
                    'model_size_mb': model_size_mb,
                    'is_lightweight': is_lightweight,
                    'is_compact': is_compact
                }
                
                results['requirements_met']['efficiency'] = is_lightweight and is_compact
            
        except Exception as e:
            print(f"✗ Efficiency analysis failed: {e}")
            results['validation_results']['efficiency'] = {'error': str(e)}
            results['requirements_met']['efficiency'] = False
        
        # 5. Overall Assessment
        print(f"\n5. Overall Assessment...")
        print("-" * 30)
        
        requirements = [
            ('Model Loading', results['validation_results'].get('model_loading', False)),
            ('FPS Target', results['requirements_met'].get('fps_target', False)),
            ('SSIM Target', results['requirements_met'].get('ssim_target', False)),
            ('Model Efficiency', results['requirements_met'].get('efficiency', False))
        ]
        
        passed_requirements = sum(1 for _, passed in requirements if passed)
        total_requirements = len(requirements)
        
        print(f"Requirements Summary:")
        for req_name, passed in requirements:
            status = "✓" if passed else "✗"
            print(f"  {status} {req_name}")
        
        print(f"\nOverall: {passed_requirements}/{total_requirements} requirements met")
        
        # Overall pass criteria: all critical requirements met
        critical_requirements = ['fps_target', 'ssim_target']
        all_critical_met = all(results['requirements_met'].get(req, False) for req in critical_requirements)
        
        results['overall_pass'] = all_critical_met
        results['requirements_summary'] = {
            'passed': passed_requirements,
            'total': total_requirements,
            'critical_met': all_critical_met
        }
        
        if results['overall_pass']:
            print(f"\n🎉 VALIDATION PASSED! Model meets all critical requirements.")
        else:
            print(f"\n❌ VALIDATION FAILED. Critical requirements not met.")
            
            # Provide recommendations
            print(f"\nRecommendations:")
            if not results['requirements_met'].get('fps_target', False):
                print("- Optimize model for faster inference (quantization, pruning)")
                print("- Consider using a smaller model architecture")
                print("- Apply TensorRT or ONNX optimizations")
            
            if not results['requirements_met'].get('ssim_target', False):
                print("- Train for more epochs")
                print("- Adjust knowledge distillation parameters")
                print("- Use a better teacher model")
                print("- Increase model capacity slightly")
        
    except Exception as e:
        print(f"Validation failed with error: {e}")
        results['validation_results']['error'] = str(e)
        import traceback
        traceback.print_exc()
    
    return results


def main():
    """Main validation function."""
    parser = argparse.ArgumentParser(description='Final model validation')
    parser.add_argument('--model-path', type=str, required=True,
                       help='Path to trained model')
    parser.add_argument('--benchmark-dir', type=str, default='data/benchmark',
                       help='Benchmark dataset directory')
    parser.add_argument('--output-dir', type=str, default='results/final_validation',
                       help='Output directory for validation results')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cpu', 'cuda'], help='Device to use')
    parser.add_argument('--target-ssim', type=float, default=0.90,
                       help='Target SSIM score')
    parser.add_argument('--target-fps', type=float, default=30.0,
                       help='Target FPS performance')
    
    args = parser.parse_args()
    
    # Set device
    device = args.device
    if device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        device = 'cpu'
    
    # Check model exists
    if not os.path.exists(args.model_path):
        print(f"Error: Model not found at {args.model_path}")
        return
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Run validation
    results = validate_model_requirements(
        model_path=args.model_path,
        benchmark_dir=args.benchmark_dir,
        device=device,
        target_ssim=args.target_ssim,
        target_fps=args.target_fps
    )
    
    # Save results
    results_file = os.path.join(args.output_dir, 'validation_results.json')
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    # Generate summary report
    report_file = os.path.join(args.output_dir, 'validation_report.txt')
    with open(report_file, 'w') as f:
        f.write("Image Sharpening Model - Final Validation Report\n")
        f.write("=" * 60 + "\n\n")
        
        f.write(f"Model: {args.model_path}\n")
        f.write(f"Validation Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Device: {device}\n\n")
        
        f.write("Requirements:\n")
        f.write(f"- Target SSIM: {args.target_ssim}\n")
        f.write(f"- Target FPS: {args.target_fps}\n")
        f.write(f"- Target Resolution: 1920x1080\n\n")
        
        f.write("Validation Results:\n")
        f.write("-" * 20 + "\n")
        
        if 'requirements_summary' in results:
            summary = results['requirements_summary']
            f.write(f"Overall: {summary['passed']}/{summary['total']} requirements met\n")
            f.write(f"Critical requirements: {'PASSED' if summary['critical_met'] else 'FAILED'}\n\n")
        
        # Detailed results
        for req_name, passed in [
            ('Model Loading', results['validation_results'].get('model_loading', False)),
            ('FPS Target', results['requirements_met'].get('fps_target', False)),
            ('SSIM Target', results['requirements_met'].get('ssim_target', False)),
            ('Model Efficiency', results['requirements_met'].get('efficiency', False))
        ]:
            status = "PASS" if passed else "FAIL"
            f.write(f"{req_name}: {status}\n")
        
        # Performance details
        if 'performance' in results['validation_results']:
            f.write(f"\nPerformance Details:\n")
            for resolution, perf_data in results['validation_results']['performance'].items():
                if 'fps' in perf_data:
                    f.write(f"  {resolution}: {perf_data['fps']:.1f} FPS\n")
        
        # Accuracy details
        if 'accuracy' in results['validation_results']:
            acc_data = results['validation_results']['accuracy']
            if 'avg_ssim' in acc_data:
                f.write(f"\nAccuracy Details:\n")
                f.write(f"  Average SSIM: {acc_data['avg_ssim']:.4f}\n")
                f.write(f"  Images above target: {acc_data['percentage_above_target']:.1f}%\n")
    
    print(f"\nValidation results saved to:")
    print(f"  - {results_file}")
    print(f"  - {report_file}")
    
    # Final status
    if results['overall_pass']:
        print(f"\n🎉 MODEL VALIDATION SUCCESSFUL!")
        print(f"The model is ready for deployment.")
    else:
        print(f"\n❌ MODEL VALIDATION FAILED!")
        print(f"Please address the issues before deployment.")


if __name__ == '__main__':
    main()
