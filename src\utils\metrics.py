"""Evaluation metrics for image quality assessment."""

import torch
import torch.nn.functional as F
import numpy as np
from skimage.metrics import structural_similarity as ssim
from skimage.metrics import peak_signal_noise_ratio as psnr
from typing import Union, Tuple
import lpips


class ImageMetrics:
    """Collection of image quality metrics."""
    
    def __init__(self, device: str = 'cuda'):
        """Initialize metrics.
        
        Args:
            device: Device to run computations on
        """
        self.device = device
        self.lpips_fn = lpips.LPIPS(net='alex').to(device)
    
    def calculate_ssim(self, 
                      img1: Union[torch.Tensor, np.ndarray], 
                      img2: Union[torch.Tensor, np.ndarray],
                      data_range: float = 1.0) -> float:
        """Calculate Structural Similarity Index (SSIM).
        
        Args:
            img1: First image
            img2: Second image
            data_range: Data range of the images
            
        Returns:
            SSIM value
        """
        if isinstance(img1, torch.Tensor):
            img1 = img1.cpu().numpy()
        if isinstance(img2, torch.Tensor):
            img2 = img2.cpu().numpy()
            
        # Handle batch dimension
        if img1.ndim == 4:
            ssim_values = []
            for i in range(img1.shape[0]):
                # Convert from CHW to HWC for skimage
                im1 = np.transpose(img1[i], (1, 2, 0))
                im2 = np.transpose(img2[i], (1, 2, 0))
                
                if im1.shape[2] == 1:  # Grayscale
                    im1 = im1.squeeze(2)
                    im2 = im2.squeeze(2)
                    
                ssim_val = ssim(im1, im2, data_range=data_range, 
                               channel_axis=2 if im1.ndim == 3 else None)
                ssim_values.append(ssim_val)
            return np.mean(ssim_values)
        else:
            # Single image
            if img1.ndim == 3:
                img1 = np.transpose(img1, (1, 2, 0))
                img2 = np.transpose(img2, (1, 2, 0))
                
            if img1.shape[2] == 1:  # Grayscale
                img1 = img1.squeeze(2)
                img2 = img2.squeeze(2)
                
            return ssim(img1, img2, data_range=data_range,
                       channel_axis=2 if img1.ndim == 3 else None)
    
    def calculate_psnr(self, 
                      img1: Union[torch.Tensor, np.ndarray], 
                      img2: Union[torch.Tensor, np.ndarray],
                      data_range: float = 1.0) -> float:
        """Calculate Peak Signal-to-Noise Ratio (PSNR).
        
        Args:
            img1: First image
            img2: Second image
            data_range: Data range of the images
            
        Returns:
            PSNR value in dB
        """
        if isinstance(img1, torch.Tensor):
            img1 = img1.cpu().numpy()
        if isinstance(img2, torch.Tensor):
            img2 = img2.cpu().numpy()
            
        # Handle batch dimension
        if img1.ndim == 4:
            psnr_values = []
            for i in range(img1.shape[0]):
                # Convert from CHW to HWC for skimage
                im1 = np.transpose(img1[i], (1, 2, 0))
                im2 = np.transpose(img2[i], (1, 2, 0))
                
                psnr_val = psnr(im1, im2, data_range=data_range)
                psnr_values.append(psnr_val)
            return np.mean(psnr_values)
        else:
            # Single image
            if img1.ndim == 3:
                img1 = np.transpose(img1, (1, 2, 0))
                img2 = np.transpose(img2, (1, 2, 0))
                
            return psnr(img1, img2, data_range=data_range)
    
    def calculate_lpips(self, 
                       img1: torch.Tensor, 
                       img2: torch.Tensor) -> float:
        """Calculate Learned Perceptual Image Patch Similarity (LPIPS).
        
        Args:
            img1: First image tensor (B, C, H, W) in range [-1, 1]
            img2: Second image tensor (B, C, H, W) in range [-1, 1]
            
        Returns:
            LPIPS value
        """
        with torch.no_grad():
            # Ensure images are in range [-1, 1]
            if img1.max() > 1.0 or img1.min() < -1.0:
                img1 = img1 * 2.0 - 1.0
            if img2.max() > 1.0 or img2.min() < -1.0:
                img2 = img2 * 2.0 - 1.0
                
            lpips_val = self.lpips_fn(img1.to(self.device), img2.to(self.device))
            return lpips_val.mean().item()
    
    def calculate_all_metrics(self, 
                             pred: torch.Tensor, 
                             target: torch.Tensor) -> dict:
        """Calculate all metrics for a batch of images.
        
        Args:
            pred: Predicted images (B, C, H, W) in range [0, 1]
            target: Target images (B, C, H, W) in range [0, 1]
            
        Returns:
            Dictionary containing all metrics
        """
        metrics = {}
        
        # SSIM
        metrics['ssim'] = self.calculate_ssim(pred, target, data_range=1.0)
        
        # PSNR
        metrics['psnr'] = self.calculate_psnr(pred, target, data_range=1.0)
        
        # LPIPS
        metrics['lpips'] = self.calculate_lpips(pred, target)
        
        return metrics


def calculate_fps(model: torch.nn.Module, 
                 input_size: Tuple[int, int, int, int],
                 device: str = 'cuda',
                 num_runs: int = 100) -> float:
    """Calculate model inference FPS.
    
    Args:
        model: PyTorch model
        input_size: Input tensor size (B, C, H, W)
        device: Device to run inference on
        num_runs: Number of inference runs for averaging
        
    Returns:
        Average FPS
    """
    model.eval()
    model = model.to(device)
    
    # Warm up
    dummy_input = torch.randn(input_size).to(device)
    with torch.no_grad():
        for _ in range(10):
            _ = model(dummy_input)
    
    # Measure inference time
    torch.cuda.synchronize() if device == 'cuda' else None
    
    import time
    start_time = time.time()
    
    with torch.no_grad():
        for _ in range(num_runs):
            _ = model(dummy_input)
            
    torch.cuda.synchronize() if device == 'cuda' else None
    end_time = time.time()
    
    total_time = end_time - start_time
    avg_time_per_inference = total_time / num_runs
    fps = 1.0 / avg_time_per_inference
    
    return fps
