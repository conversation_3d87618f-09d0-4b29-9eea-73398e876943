"""Main entry point for the image sharpening project."""

import argparse
import torch
import os
from pathlib import Path

from src.utils.config import load_config
from src.utils.metrics import ImageMetrics


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Image Sharpening using Knowledge Distillation')
    parser.add_argument('--config', type=str, default='config.yaml',
                       help='Path to configuration file')
    parser.add_argument('--mode', type=str, choices=['train', 'eval', 'demo'],
                       default='train', help='Mode to run')
    parser.add_argument('--checkpoint', type=str, default=None,
                       help='Path to model checkpoint')
    parser.add_argument('--input', type=str, default=None,
                       help='Input image path for demo mode')
    parser.add_argument('--output', type=str, default=None,
                       help='Output image path for demo mode')
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Create necessary directories
    config.create_directories()
    
    # Set device
    device = torch.device(config.get('hardware.device', 'cuda') 
                         if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    if args.mode == 'train':
        print("Starting training...")
        # TODO: Implement training
        print("Training not implemented yet. Please implement the training pipeline.")
        
    elif args.mode == 'eval':
        print("Starting evaluation...")
        # TODO: Implement evaluation
        print("Evaluation not implemented yet. Please implement the evaluation pipeline.")
        
    elif args.mode == 'demo':
        print("Running demo...")
        if args.input is None:
            print("Please provide input image path with --input")
            return
        # TODO: Implement demo
        print("Demo not implemented yet. Please implement the demo pipeline.")
    
    print("Done!")


if __name__ == '__main__':
    main()
