"""Optimized inference pipeline for real-time image sharpening."""

import torch
import torch.nn as nn
import numpy as np
import cv2
import time
from typing import Union, Tuple, Optional, Dict
import onnxruntime as ort
from pathlib import Path


class OptimizedInferenceEngine:
    """Optimized inference engine for real-time image sharpening."""
    
    def __init__(self,
                 model_path: str,
                 device: str = 'cuda',
                 use_half_precision: bool = True,
                 use_tensorrt: bool = False,
                 batch_size: int = 1):
        """Initialize optimized inference engine.
        
        Args:
            model_path: Path to the model file
            device: Device for inference ('cuda', 'cpu')
            use_half_precision: Whether to use FP16 precision
            use_tensorrt: Whether to use TensorRT optimization
            batch_size: Batch size for inference
        """
        self.device = device
        self.use_half_precision = use_half_precision and device == 'cuda'
        self.use_tensorrt = use_tensorrt
        self.batch_size = batch_size
        
        # Load model
        self.model = self._load_model(model_path)
        
        # Preprocessing parameters
        self.input_mean = torch.tensor([0.0, 0.0, 0.0]).view(1, 3, 1, 1)
        self.input_std = torch.tensor([1.0, 1.0, 1.0]).view(1, 3, 1, 1)
        
        if self.device == 'cuda':
            self.input_mean = self.input_mean.cuda()
            self.input_std = self.input_std.cuda()
        
        # Warm up the model
        self._warmup()
    
    def _load_model(self, model_path: str) -> Union[nn.Module, ort.InferenceSession]:
        """Load and optimize model.
        
        Args:
            model_path: Path to model file
            
        Returns:
            Loaded model
        """
        model_path = Path(model_path)
        
        if model_path.suffix == '.onnx':
            return self._load_onnx_model(str(model_path))
        elif model_path.suffix == '.pth':
            return self._load_pytorch_model(str(model_path))
        elif model_path.suffix == '.pt':
            return self._load_torchscript_model(str(model_path))
        else:
            raise ValueError(f"Unsupported model format: {model_path.suffix}")
    
    def _load_pytorch_model(self, model_path: str) -> nn.Module:
        """Load PyTorch model.
        
        Args:
            model_path: Path to PyTorch model
            
        Returns:
            Loaded PyTorch model
        """
        from ..models.student_models import StudentModelFactory
        
        checkpoint = torch.load(model_path, map_location=self.device)
        
        # Extract model configuration
        if 'config' in checkpoint:
            config = checkpoint['config']
            student_config = config.get('model', {}).get('student', {})
            model_name = student_config.get('name', 'lightweightsr')
            
            # Create model
            model = StudentModelFactory.create_model(model_name, **student_config)
        else:
            # Fallback to default model
            from ..models.student_models import LightweightSR
            model = LightweightSR()
        
        # Load state dict
        if 'student_model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['student_model_state_dict'])
        elif 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        
        model = model.to(self.device)
        model.eval()
        
        # Apply optimizations
        if self.use_half_precision:
            model = model.half()
        
        # Compile model for faster inference (PyTorch 2.0+)
        try:
            model = torch.compile(model, mode='max-autotune')
        except:
            pass  # Compilation not available
        
        return model
    
    def _load_torchscript_model(self, model_path: str) -> torch.jit.ScriptModule:
        """Load TorchScript model.
        
        Args:
            model_path: Path to TorchScript model
            
        Returns:
            Loaded TorchScript model
        """
        model = torch.jit.load(model_path, map_location=self.device)
        model.eval()
        
        if self.use_half_precision:
            model = model.half()
        
        return model
    
    def _load_onnx_model(self, model_path: str) -> ort.InferenceSession:
        """Load ONNX model.
        
        Args:
            model_path: Path to ONNX model
            
        Returns:
            ONNX inference session
        """
        providers = []
        
        if self.device == 'cuda':
            providers.append('CUDAExecutionProvider')
        providers.append('CPUExecutionProvider')
        
        session_options = ort.SessionOptions()
        session_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        
        if self.use_tensorrt and self.device == 'cuda':
            providers.insert(0, 'TensorrtExecutionProvider')
        
        session = ort.InferenceSession(
            model_path,
            sess_options=session_options,
            providers=providers
        )
        
        return session
    
    def _warmup(self, num_warmup: int = 10):
        """Warm up the model for consistent timing.
        
        Args:
            num_warmup: Number of warmup iterations
        """
        # Create dummy input
        dummy_input = torch.randn(
            self.batch_size, 3, 256, 256,
            device=self.device
        )
        
        if self.use_half_precision:
            dummy_input = dummy_input.half()
        
        # Warmup
        with torch.no_grad():
            for _ in range(num_warmup):
                _ = self.forward(dummy_input)
        
        # Synchronize if using CUDA
        if self.device == 'cuda':
            torch.cuda.synchronize()
    
    def preprocess_image(self, image: np.ndarray) -> torch.Tensor:
        """Preprocess image for inference.
        
        Args:
            image: Input image (H, W, C) in BGR format
            
        Returns:
            Preprocessed tensor
        """
        # Convert BGR to RGB
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Convert to float and normalize to [0, 1]
        image = image.astype(np.float32) / 255.0
        
        # Convert to tensor and add batch dimension
        tensor = torch.from_numpy(image.transpose(2, 0, 1)).unsqueeze(0)
        tensor = tensor.to(self.device)
        
        if self.use_half_precision:
            tensor = tensor.half()
        
        # Apply normalization
        tensor = (tensor - self.input_mean) / self.input_std
        
        return tensor
    
    def postprocess_output(self, output: torch.Tensor) -> np.ndarray:
        """Postprocess model output to image.
        
        Args:
            output: Model output tensor
            
        Returns:
            Output image (H, W, C) in BGR format
        """
        # Remove batch dimension and move to CPU
        if isinstance(output, torch.Tensor):
            output = output.squeeze(0).cpu()
            
            if output.dtype == torch.float16:
                output = output.float()
            
            output = output.numpy()
        
        # Convert from CHW to HWC
        output = output.transpose(1, 2, 0)
        
        # Denormalize
        output = output * self.input_std.cpu().numpy().squeeze() + self.input_mean.cpu().numpy().squeeze()
        
        # Clip to [0, 1] and convert to uint8
        output = np.clip(output, 0, 1)
        output = (output * 255).astype(np.uint8)
        
        # Convert RGB to BGR
        output = cv2.cvtColor(output, cv2.COLOR_RGB2BGR)
        
        return output
    
    def forward(self, input_tensor: torch.Tensor) -> torch.Tensor:
        """Forward pass through the model.
        
        Args:
            input_tensor: Input tensor
            
        Returns:
            Output tensor
        """
        if isinstance(self.model, ort.InferenceSession):
            # ONNX inference
            input_name = self.model.get_inputs()[0].name
            output_name = self.model.get_outputs()[0].name
            
            # Convert to numpy
            input_np = input_tensor.cpu().numpy()
            
            # Run inference
            output_np = self.model.run([output_name], {input_name: input_np})[0]
            
            # Convert back to tensor
            output = torch.from_numpy(output_np).to(self.device)
            
        else:
            # PyTorch inference
            with torch.no_grad():
                output = self.model(input_tensor)
        
        return output
    
    def infer_image(self, image: np.ndarray) -> Tuple[np.ndarray, float]:
        """Infer on a single image.
        
        Args:
            image: Input image (H, W, C) in BGR format
            
        Returns:
            Tuple of (output_image, inference_time)
        """
        # Preprocess
        input_tensor = self.preprocess_image(image)
        
        # Measure inference time
        if self.device == 'cuda':
            torch.cuda.synchronize()
        
        start_time = time.time()
        
        # Forward pass
        output_tensor = self.forward(input_tensor)
        
        if self.device == 'cuda':
            torch.cuda.synchronize()
        
        end_time = time.time()
        inference_time = end_time - start_time
        
        # Postprocess
        output_image = self.postprocess_output(output_tensor)
        
        return output_image, inference_time
    
    def benchmark(self, 
                 input_shape: Tuple[int, int] = (1920, 1080),
                 num_runs: int = 100) -> Dict[str, float]:
        """Benchmark the inference engine.
        
        Args:
            input_shape: Input image shape (H, W)
            num_runs: Number of benchmark runs
            
        Returns:
            Benchmark results
        """
        # Create test image
        test_image = np.random.randint(0, 256, (*input_shape, 3), dtype=np.uint8)
        
        # Warmup
        for _ in range(10):
            _, _ = self.infer_image(test_image)
        
        # Benchmark
        inference_times = []
        
        for _ in range(num_runs):
            _, inference_time = self.infer_image(test_image)
            inference_times.append(inference_time)
        
        # Calculate statistics
        inference_times = np.array(inference_times)
        
        results = {
            'avg_inference_time': np.mean(inference_times),
            'min_inference_time': np.min(inference_times),
            'max_inference_time': np.max(inference_times),
            'std_inference_time': np.std(inference_times),
            'avg_fps': 1.0 / np.mean(inference_times),
            'max_fps': 1.0 / np.min(inference_times),
            'min_fps': 1.0 / np.max(inference_times)
        }
        
        return results


class VideoProcessor:
    """Real-time video processor for image sharpening."""
    
    def __init__(self, inference_engine: OptimizedInferenceEngine):
        """Initialize video processor.
        
        Args:
            inference_engine: Optimized inference engine
        """
        self.inference_engine = inference_engine
        
    def process_video(self,
                     input_path: str,
                     output_path: str,
                     target_fps: Optional[float] = None) -> Dict[str, float]:
        """Process video file.
        
        Args:
            input_path: Path to input video
            output_path: Path to output video
            target_fps: Target FPS (uses input FPS if None)
            
        Returns:
            Processing statistics
        """
        # Open input video
        cap = cv2.VideoCapture(input_path)
        
        if not cap.isOpened():
            raise ValueError(f"Could not open video: {input_path}")
        
        # Get video properties
        input_fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        if target_fps is None:
            target_fps = input_fps
        
        # Setup output video
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, target_fps, (width, height))
        
        # Process frames
        frame_times = []
        processed_frames = 0
        
        print(f"Processing {total_frames} frames at {target_fps} FPS...")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Process frame
            start_time = time.time()
            processed_frame, inference_time = self.inference_engine.infer_image(frame)
            end_time = time.time()
            
            frame_time = end_time - start_time
            frame_times.append(frame_time)
            
            # Write frame
            out.write(processed_frame)
            processed_frames += 1
            
            # Progress update
            if processed_frames % 100 == 0:
                avg_fps = 1.0 / np.mean(frame_times[-100:])
                print(f"Processed {processed_frames}/{total_frames} frames, "
                      f"Current FPS: {avg_fps:.1f}")
        
        # Cleanup
        cap.release()
        out.release()
        
        # Calculate statistics
        frame_times = np.array(frame_times)
        
        stats = {
            'total_frames': processed_frames,
            'avg_frame_time': np.mean(frame_times),
            'avg_fps': 1.0 / np.mean(frame_times),
            'min_fps': 1.0 / np.max(frame_times),
            'max_fps': 1.0 / np.min(frame_times),
            'target_fps': target_fps,
            'input_fps': input_fps
        }
        
        return stats
