"""Comprehensive inference benchmarking script."""

import os
import argparse
import torch
import cv2
import numpy as np
import json
import time
from pathlib import Path
import sys

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.inference.optimized_inference import OptimizedInferenceEngine
from src.utils.config import load_config
from src.utils.metrics import ImageMetrics


def create_test_images(output_dir: str, resolutions: list) -> dict:
    """Create test images for benchmarking.
    
    Args:
        output_dir: Directory to save test images
        resolutions: List of (width, height) tuples
        
    Returns:
        Dictionary mapping resolution to image path
    """
    os.makedirs(output_dir, exist_ok=True)
    
    test_images = {}
    
    for width, height in resolutions:
        # Create test image with various patterns
        image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Add gradient background
        for i in range(height):
            for j in range(width):
                image[i, j] = [
                    int(255 * i / height),
                    int(255 * j / width),
                    128
                ]
        
        # Add geometric shapes
        cv2.rectangle(image, (width//4, height//4), (width//2, height//2), (255, 0, 0), -1)
        cv2.circle(image, (3*width//4, height//4), min(width, height)//8, (0, 255, 0), -1)
        
        # Add text
        font_scale = max(1, min(width, height) // 500)
        cv2.putText(image, f"{width}x{height}", (50, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, font_scale, (255, 255, 255), 2)
        
        # Save image
        image_path = os.path.join(output_dir, f"test_{width}x{height}.png")
        cv2.imwrite(image_path, image)
        
        test_images[f"{width}x{height}"] = image_path
    
    return test_images


def benchmark_model_formats(model_paths: dict, 
                           test_image_path: str,
                           device: str = 'cuda',
                           num_runs: int = 100) -> dict:
    """Benchmark different model formats.
    
    Args:
        model_paths: Dictionary of format -> path
        test_image_path: Path to test image
        device: Device for inference
        num_runs: Number of benchmark runs
        
    Returns:
        Benchmark results
    """
    results = {}
    
    # Load test image
    test_image = cv2.imread(test_image_path)
    if test_image is None:
        raise ValueError(f"Could not load test image: {test_image_path}")
    
    for format_name, model_path in model_paths.items():
        if not os.path.exists(model_path):
            print(f"Model not found: {model_path}")
            continue
        
        print(f"\nBenchmarking {format_name} format...")
        
        try:
            # Create inference engine
            engine = OptimizedInferenceEngine(
                model_path=model_path,
                device=device,
                use_half_precision=(device == 'cuda')
            )
            
            # Warmup
            for _ in range(10):
                _, _ = engine.infer_image(test_image)
            
            # Benchmark
            inference_times = []
            
            for _ in range(num_runs):
                _, inference_time = engine.infer_image(test_image)
                inference_times.append(inference_time)
            
            # Calculate statistics
            inference_times = np.array(inference_times)
            
            results[format_name] = {
                'avg_inference_time': float(np.mean(inference_times)),
                'min_inference_time': float(np.min(inference_times)),
                'max_inference_time': float(np.max(inference_times)),
                'std_inference_time': float(np.std(inference_times)),
                'avg_fps': float(1.0 / np.mean(inference_times)),
                'max_fps': float(1.0 / np.min(inference_times)),
                'min_fps': float(1.0 / np.max(inference_times))
            }
            
            print(f"  Average FPS: {results[format_name]['avg_fps']:.1f}")
            print(f"  Average time: {results[format_name]['avg_inference_time']*1000:.2f}ms")
            
        except Exception as e:
            print(f"  Error: {e}")
            results[format_name] = {'error': str(e)}
    
    return results


def benchmark_resolutions(model_path: str,
                         test_images: dict,
                         device: str = 'cuda',
                         num_runs: int = 50) -> dict:
    """Benchmark different input resolutions.
    
    Args:
        model_path: Path to model
        test_images: Dictionary of resolution -> image path
        device: Device for inference
        num_runs: Number of benchmark runs
        
    Returns:
        Benchmark results by resolution
    """
    results = {}
    
    print(f"\nBenchmarking resolutions with {model_path}...")
    
    try:
        # Create inference engine
        engine = OptimizedInferenceEngine(
            model_path=model_path,
            device=device,
            use_half_precision=(device == 'cuda')
        )
        
        for resolution, image_path in test_images.items():
            print(f"  Testing {resolution}...")
            
            # Load test image
            test_image = cv2.imread(image_path)
            
            # Warmup
            for _ in range(5):
                _, _ = engine.infer_image(test_image)
            
            # Benchmark
            inference_times = []
            
            for _ in range(num_runs):
                _, inference_time = engine.infer_image(test_image)
                inference_times.append(inference_time)
            
            # Calculate statistics
            inference_times = np.array(inference_times)
            
            results[resolution] = {
                'avg_inference_time': float(np.mean(inference_times)),
                'avg_fps': float(1.0 / np.mean(inference_times)),
                'max_fps': float(1.0 / np.min(inference_times)),
                'min_fps': float(1.0 / np.max(inference_times)),
                'image_size': test_image.shape[:2]
            }
            
            print(f"    FPS: {results[resolution]['avg_fps']:.1f}")
    
    except Exception as e:
        print(f"Error benchmarking resolutions: {e}")
        return {}
    
    return results


def benchmark_batch_sizes(model_path: str,
                         test_image_path: str,
                         batch_sizes: list,
                         device: str = 'cuda') -> dict:
    """Benchmark different batch sizes.
    
    Args:
        model_path: Path to model
        test_image_path: Path to test image
        batch_sizes: List of batch sizes to test
        device: Device for inference
        
    Returns:
        Benchmark results by batch size
    """
    results = {}
    
    print(f"\nBenchmarking batch sizes...")
    
    # Load test image
    test_image = cv2.imread(test_image_path)
    
    for batch_size in batch_sizes:
        print(f"  Testing batch size {batch_size}...")
        
        try:
            # Create inference engine with specific batch size
            engine = OptimizedInferenceEngine(
                model_path=model_path,
                device=device,
                use_half_precision=(device == 'cuda'),
                batch_size=batch_size
            )
            
            # Create batch of images
            batch_images = [test_image] * batch_size
            
            # Warmup
            for _ in range(5):
                for img in batch_images:
                    _, _ = engine.infer_image(img)
            
            # Benchmark
            start_time = time.time()
            
            for _ in range(20):  # Fewer runs for batch testing
                for img in batch_images:
                    _, _ = engine.infer_image(img)
            
            end_time = time.time()
            
            total_time = end_time - start_time
            total_images = 20 * batch_size
            avg_time_per_image = total_time / total_images
            
            results[batch_size] = {
                'avg_time_per_image': avg_time_per_image,
                'avg_fps': 1.0 / avg_time_per_image,
                'total_time': total_time,
                'total_images': total_images
            }
            
            print(f"    FPS per image: {results[batch_size]['avg_fps']:.1f}")
            
        except Exception as e:
            print(f"    Error: {e}")
            results[batch_size] = {'error': str(e)}
    
    return results


def main():
    """Main benchmarking function."""
    parser = argparse.ArgumentParser(description='Benchmark inference performance')
    parser.add_argument('--model-path', type=str, required=True,
                       help='Path to trained model')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cpu', 'cuda'], help='Device to use')
    parser.add_argument('--output-dir', type=str, default='results/benchmarks',
                       help='Output directory for results')
    parser.add_argument('--num-runs', type=int, default=100,
                       help='Number of benchmark runs')
    parser.add_argument('--test-formats', action='store_true',
                       help='Test different model formats (requires converted models)')
    parser.add_argument('--test-resolutions', action='store_true',
                       help='Test different input resolutions')
    parser.add_argument('--test-batch-sizes', action='store_true',
                       help='Test different batch sizes')
    
    args = parser.parse_args()
    
    # Set device
    device = args.device
    if device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        device = 'cpu'
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("Inference Performance Benchmarking")
    print("=" * 50)
    print(f"Model: {args.model_path}")
    print(f"Device: {device}")
    print(f"Number of runs: {args.num_runs}")
    
    # Check if model exists
    if not os.path.exists(args.model_path):
        print(f"Model not found: {args.model_path}")
        return
    
    # Create test images
    test_dir = os.path.join(args.output_dir, 'test_images')
    
    # Standard resolutions for testing
    resolutions = [
        (256, 256),    # Training size
        (512, 512),    # Medium size
        (1280, 720),   # HD
        (1920, 1080),  # Full HD (target)
        (2560, 1440),  # QHD
        (3840, 2160)   # 4K
    ]
    
    test_images = create_test_images(test_dir, resolutions)
    print(f"Created test images in: {test_dir}")
    
    # Main benchmark results
    all_results = {
        'model_path': args.model_path,
        'device': device,
        'timestamp': time.time()
    }
    
    # Basic benchmark with 1920x1080 (target resolution)
    target_image = test_images.get('1920x1080')
    if target_image:
        print(f"\nBasic benchmark at 1920x1080...")
        
        try:
            engine = OptimizedInferenceEngine(
                model_path=args.model_path,
                device=device,
                use_half_precision=(device == 'cuda')
            )
            
            basic_results = engine.benchmark(
                input_shape=(1080, 1920),
                num_runs=args.num_runs
            )
            
            all_results['basic_benchmark'] = basic_results
            
            print(f"Average FPS: {basic_results['avg_fps']:.1f}")
            print(f"Average inference time: {basic_results['avg_inference_time']*1000:.2f}ms")
            
            # Check if target FPS is achieved
            target_fps = 30
            if basic_results['avg_fps'] >= target_fps:
                print(f"✓ Target {target_fps} FPS achieved!")
            else:
                print(f"✗ Target {target_fps} FPS not achieved")
            
        except Exception as e:
            print(f"Basic benchmark failed: {e}")
    
    # Test different model formats
    if args.test_formats:
        model_dir = os.path.dirname(args.model_path)
        model_name = os.path.splitext(os.path.basename(args.model_path))[0]
        
        format_paths = {
            'pytorch': args.model_path,
            'torchscript': os.path.join(model_dir, f"{model_name}_torchscript.pt"),
            'onnx': os.path.join(model_dir, f"{model_name}_model.onnx")
        }
        
        format_results = benchmark_model_formats(
            format_paths, target_image, device, args.num_runs
        )
        all_results['format_benchmark'] = format_results
    
    # Test different resolutions
    if args.test_resolutions:
        resolution_results = benchmark_resolutions(
            args.model_path, test_images, device, args.num_runs // 2
        )
        all_results['resolution_benchmark'] = resolution_results
    
    # Test different batch sizes
    if args.test_batch_sizes:
        batch_sizes = [1, 2, 4, 8] if device == 'cuda' else [1, 2]
        batch_results = benchmark_batch_sizes(
            args.model_path, target_image, batch_sizes, device
        )
        all_results['batch_benchmark'] = batch_results
    
    # Save results
    results_file = os.path.join(args.output_dir, 'benchmark_results.json')
    with open(results_file, 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\nBenchmark results saved to: {results_file}")
    
    # Print summary
    print("\n" + "=" * 50)
    print("BENCHMARK SUMMARY")
    print("=" * 50)
    
    if 'basic_benchmark' in all_results:
        basic = all_results['basic_benchmark']
        print(f"Target Resolution (1920x1080):")
        print(f"  Average FPS: {basic['avg_fps']:.1f}")
        print(f"  Min FPS: {basic['min_fps']:.1f}")
        print(f"  Max FPS: {basic['max_fps']:.1f}")
        print(f"  Average time: {basic['avg_inference_time']*1000:.2f}ms")
    
    if 'resolution_benchmark' in all_results:
        print(f"\nResolution Performance:")
        for res, data in all_results['resolution_benchmark'].items():
            print(f"  {res}: {data['avg_fps']:.1f} FPS")
    
    if 'format_benchmark' in all_results:
        print(f"\nFormat Performance:")
        for fmt, data in all_results['format_benchmark'].items():
            if 'avg_fps' in data:
                print(f"  {fmt}: {data['avg_fps']:.1f} FPS")
    
    print("\nBenchmarking completed!")


if __name__ == '__main__':
    main()
