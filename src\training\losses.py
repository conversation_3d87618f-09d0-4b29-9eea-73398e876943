"""Loss functions for knowledge distillation training."""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Tuple
import lpips


class DistillationLoss(nn.Module):
    """Knowledge distillation loss combining multiple loss components."""
    
    def __init__(self,
                 temperature: float = 4.0,
                 alpha: float = 0.7,
                 beta: float = 0.3,
                 pixel_loss_type: str = 'l1',
                 use_perceptual_loss: bool = True,
                 use_feature_distillation: bool = True,
                 device: str = 'cuda'):
        """Initialize distillation loss.
        
        Args:
            temperature: Temperature for knowledge distillation
            alpha: Weight for distillation loss
            beta: Weight for task loss
            pixel_loss_type: Type of pixel loss ('l1', 'l2', 'huber')
            use_perceptual_loss: Whether to use perceptual loss
            use_feature_distillation: Whether to use feature-level distillation
            device: Device for computation
        """
        super().__init__()
        
        self.temperature = temperature
        self.alpha = alpha
        self.beta = beta
        self.use_perceptual_loss = use_perceptual_loss
        self.use_feature_distillation = use_feature_distillation
        
        # Pixel loss
        if pixel_loss_type.lower() == 'l1':
            self.pixel_loss = nn.L1Loss()
        elif pixel_loss_type.lower() == 'l2':
            self.pixel_loss = nn.MSELoss()
        elif pixel_loss_type.lower() == 'huber':
            self.pixel_loss = nn.HuberLoss()
        else:
            self.pixel_loss = nn.L1Loss()
        
        # Perceptual loss
        if use_perceptual_loss:
            self.perceptual_loss = lpips.LPIPS(net='alex').to(device)
            for param in self.perceptual_loss.parameters():
                param.requires_grad = False
        
        # Feature distillation loss
        self.feature_loss = nn.MSELoss()
        
        # KL divergence for output distillation
        self.kl_loss = nn.KLDivLoss(reduction='batchmean')
    
    def forward(self,
                student_output: torch.Tensor,
                teacher_output: torch.Tensor,
                target: torch.Tensor,
                student_features: Optional[Dict[str, torch.Tensor]] = None,
                teacher_features: Optional[Dict[str, torch.Tensor]] = None) -> Dict[str, torch.Tensor]:
        """Calculate distillation loss.
        
        Args:
            student_output: Student model output
            teacher_output: Teacher model output
            target: Ground truth target
            student_features: Student intermediate features
            teacher_features: Teacher intermediate features
            
        Returns:
            Dictionary of loss components
        """
        losses = {}
        
        # Task loss (student output vs ground truth)
        task_loss = self.pixel_loss(student_output, target)
        losses['task_loss'] = task_loss
        
        # Perceptual loss
        if self.use_perceptual_loss:
            # Ensure inputs are in [-1, 1] range for LPIPS
            student_norm = student_output * 2.0 - 1.0
            target_norm = target * 2.0 - 1.0
            
            perceptual_loss = self.perceptual_loss(student_norm, target_norm).mean()
            losses['perceptual_loss'] = perceptual_loss
        else:
            losses['perceptual_loss'] = torch.tensor(0.0, device=student_output.device)
        
        # Output distillation loss (student vs teacher output)
        output_distill_loss = self.pixel_loss(student_output, teacher_output.detach())
        losses['output_distill_loss'] = output_distill_loss
        
        # Feature distillation loss
        if self.use_feature_distillation and student_features and teacher_features:
            feature_distill_loss = 0.0
            feature_count = 0
            
            for key in student_features.keys():
                if key in teacher_features:
                    s_feat = student_features[key]
                    t_feat = teacher_features[key].detach()
                    
                    # Align feature dimensions if necessary
                    if s_feat.shape != t_feat.shape:
                        # Use adaptive pooling to match spatial dimensions
                        if len(s_feat.shape) == 4:  # (B, C, H, W)
                            t_feat = F.adaptive_avg_pool2d(t_feat, s_feat.shape[2:])
                        
                        # Use 1x1 conv to match channel dimensions if needed
                        if s_feat.shape[1] != t_feat.shape[1]:
                            # Skip this feature if channel mismatch is too large
                            continue
                    
                    feature_distill_loss += self.feature_loss(s_feat, t_feat)
                    feature_count += 1
            
            if feature_count > 0:
                feature_distill_loss /= feature_count
            
            losses['feature_distill_loss'] = feature_distill_loss
        else:
            losses['feature_distill_loss'] = torch.tensor(0.0, device=student_output.device)
        
        # Combined distillation loss
        distillation_loss = (
            output_distill_loss + 
            0.1 * losses['feature_distill_loss']
        )
        losses['distillation_loss'] = distillation_loss
        
        # Total loss
        total_loss = (
            self.beta * task_loss +
            self.alpha * distillation_loss +
            0.1 * losses['perceptual_loss']
        )
        losses['total_loss'] = total_loss
        
        return losses


class AdversarialLoss(nn.Module):
    """Adversarial loss for GAN-based training (optional)."""
    
    def __init__(self, loss_type: str = 'bce'):
        """Initialize adversarial loss.
        
        Args:
            loss_type: Type of adversarial loss ('bce', 'lsgan', 'wgan')
        """
        super().__init__()
        
        self.loss_type = loss_type.lower()
        
        if self.loss_type == 'bce':
            self.loss_fn = nn.BCEWithLogitsLoss()
        elif self.loss_type == 'lsgan':
            self.loss_fn = nn.MSELoss()
        elif self.loss_type == 'wgan':
            self.loss_fn = None  # WGAN uses different loss calculation
        else:
            self.loss_fn = nn.BCEWithLogitsLoss()
    
    def forward(self, 
                pred: torch.Tensor, 
                is_real: bool) -> torch.Tensor:
        """Calculate adversarial loss.
        
        Args:
            pred: Discriminator predictions
            is_real: Whether the input is real or fake
            
        Returns:
            Adversarial loss
        """
        if self.loss_type == 'wgan':
            # WGAN loss
            if is_real:
                return -pred.mean()
            else:
                return pred.mean()
        else:
            # BCE or LSGAN loss
            if is_real:
                target = torch.ones_like(pred)
            else:
                target = torch.zeros_like(pred)
            
            return self.loss_fn(pred, target)


class EdgeLoss(nn.Module):
    """Edge-aware loss for preserving sharp details."""
    
    def __init__(self):
        """Initialize edge loss."""
        super().__init__()
        
        # Sobel filters for edge detection
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32)
        
        # Expand to 3 channels and add batch dimension
        self.sobel_x = sobel_x.view(1, 1, 3, 3).repeat(3, 1, 1, 1)
        self.sobel_y = sobel_y.view(1, 1, 3, 3).repeat(3, 1, 1, 1)
        
        self.l1_loss = nn.L1Loss()
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """Calculate edge loss.
        
        Args:
            pred: Predicted image
            target: Target image
            
        Returns:
            Edge loss
        """
        device = pred.device
        
        # Move sobel filters to device
        if self.sobel_x.device != device:
            self.sobel_x = self.sobel_x.to(device)
            self.sobel_y = self.sobel_y.to(device)
        
        # Calculate edges for prediction
        pred_edge_x = F.conv2d(pred, self.sobel_x, padding=1, groups=3)
        pred_edge_y = F.conv2d(pred, self.sobel_y, padding=1, groups=3)
        pred_edge = torch.sqrt(pred_edge_x**2 + pred_edge_y**2 + 1e-8)
        
        # Calculate edges for target
        target_edge_x = F.conv2d(target, self.sobel_x, padding=1, groups=3)
        target_edge_y = F.conv2d(target, self.sobel_y, padding=1, groups=3)
        target_edge = torch.sqrt(target_edge_x**2 + target_edge_y**2 + 1e-8)
        
        # Edge loss
        edge_loss = self.l1_loss(pred_edge, target_edge)
        
        return edge_loss


class CombinedLoss(nn.Module):
    """Combined loss function with multiple components."""
    
    def __init__(self,
                 config: dict,
                 device: str = 'cuda'):
        """Initialize combined loss.
        
        Args:
            config: Loss configuration
            device: Device for computation
        """
        super().__init__()
        
        self.config = config
        
        # Main distillation loss
        self.distillation_loss = DistillationLoss(
            temperature=config.get('temperature', 4.0),
            alpha=config.get('alpha', 0.7),
            beta=config.get('beta', 0.3),
            pixel_loss_type=config.get('pixel_loss', 'l1'),
            use_perceptual_loss=config.get('use_perceptual_loss', True),
            use_feature_distillation=config.get('use_feature_distillation', True),
            device=device
        )
        
        # Optional edge loss
        if config.get('use_edge_loss', False):
            self.edge_loss = EdgeLoss()
            self.edge_weight = config.get('edge_weight', 0.1)
        else:
            self.edge_loss = None
        
        # Optional adversarial loss
        if config.get('use_adversarial_loss', False):
            self.adversarial_loss = AdversarialLoss(config.get('adversarial_type', 'bce'))
            self.adversarial_weight = config.get('adversarial_weight', 0.01)
        else:
            self.adversarial_loss = None
    
    def forward(self,
                student_output: torch.Tensor,
                teacher_output: torch.Tensor,
                target: torch.Tensor,
                student_features: Optional[Dict[str, torch.Tensor]] = None,
                teacher_features: Optional[Dict[str, torch.Tensor]] = None,
                discriminator_pred: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """Calculate combined loss.
        
        Args:
            student_output: Student model output
            teacher_output: Teacher model output
            target: Ground truth target
            student_features: Student intermediate features
            teacher_features: Teacher intermediate features
            discriminator_pred: Discriminator predictions (for adversarial loss)
            
        Returns:
            Dictionary of loss components
        """
        # Main distillation loss
        losses = self.distillation_loss(
            student_output, teacher_output, target,
            student_features, teacher_features
        )
        
        # Edge loss
        if self.edge_loss is not None:
            edge_loss = self.edge_loss(student_output, target)
            losses['edge_loss'] = edge_loss
            losses['total_loss'] += self.edge_weight * edge_loss
        
        # Adversarial loss
        if self.adversarial_loss is not None and discriminator_pred is not None:
            adv_loss = self.adversarial_loss(discriminator_pred, True)
            losses['adversarial_loss'] = adv_loss
            losses['total_loss'] += self.adversarial_weight * adv_loss
        
        return losses
