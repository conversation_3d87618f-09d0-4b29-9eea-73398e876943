"""<PERSON><PERSON><PERSON> to monitor training progress and visualize results."""

import os
import argparse
import torch
import matplotlib.pyplot as plt
import json
from pathlib import Path
import sys
from typing import Dict, List
import numpy as np

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.utils.config import load_config


def load_checkpoint_metrics(checkpoint_path: str) -> Dict:
    """Load metrics from a checkpoint file.
    
    Args:
        checkpoint_path: Path to checkpoint file
        
    Returns:
        Dictionary with checkpoint information
    """
    if not os.path.exists(checkpoint_path):
        return {}
    
    try:
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        return {
            'epoch': checkpoint.get('epoch', 0),
            'global_step': checkpoint.get('global_step', 0),
            'val_metrics': checkpoint.get('val_metrics', {}),
            'best_ssim': checkpoint.get('best_ssim', 0.0)
        }
    except Exception as e:
        print(f"Error loading checkpoint {checkpoint_path}: {e}")
        return {}


def parse_tensorboard_logs(log_dir: str) -> Dict[str, List]:
    """Parse TensorBoard logs to extract training metrics.
    
    Args:
        log_dir: Directory containing TensorBoard logs
        
    Returns:
        Dictionary with parsed metrics
    """
    try:
        from tensorboard.backend.event_processing.event_accumulator import EventAccumulator
        
        # Find the most recent run
        run_dirs = [d for d in os.listdir(log_dir) if d.startswith('run_')]
        if not run_dirs:
            return {}
        
        latest_run = max(run_dirs, key=lambda x: os.path.getctime(os.path.join(log_dir, x)))
        run_path = os.path.join(log_dir, latest_run)
        
        # Load events
        ea = EventAccumulator(run_path)
        ea.Reload()
        
        metrics = {}
        
        # Extract scalar metrics
        for tag in ea.Tags()['scalars']:
            scalar_events = ea.Scalars(tag)
            steps = [event.step for event in scalar_events]
            values = [event.value for event in scalar_events]
            metrics[tag] = {'steps': steps, 'values': values}
        
        return metrics
        
    except ImportError:
        print("TensorBoard not available for log parsing")
        return {}
    except Exception as e:
        print(f"Error parsing TensorBoard logs: {e}")
        return {}


def plot_training_curves(metrics: Dict[str, List], output_dir: str):
    """Plot training curves from metrics.
    
    Args:
        metrics: Dictionary with training metrics
        output_dir: Directory to save plots
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # Define metric groups for plotting
    metric_groups = {
        'losses': ['train/total_loss', 'epoch/val_total_loss'],
        'task_losses': ['train/task_loss', 'epoch/val_task_loss'],
        'distillation_losses': ['train/distillation_loss', 'epoch/val_distillation_loss'],
        'metrics': ['epoch/val_ssim', 'epoch/val_psnr', 'epoch/val_lpips'],
        'learning_rate': ['train/learning_rate']
    }
    
    for group_name, metric_names in metric_groups.items():
        plt.figure(figsize=(12, 6))
        
        for metric_name in metric_names:
            if metric_name in metrics:
                steps = metrics[metric_name]['steps']
                values = metrics[metric_name]['values']
                
                # Convert epoch metrics to proper scale
                if metric_name.startswith('epoch/'):
                    label = metric_name.replace('epoch/', '').replace('val_', 'Val ')
                else:
                    label = metric_name.replace('train/', '').replace('_', ' ').title()
                
                plt.plot(steps, values, label=label, linewidth=2)
        
        plt.xlabel('Step' if not group_name.startswith('epoch') else 'Epoch')
        plt.ylabel('Value')
        plt.title(f'Training Progress - {group_name.replace("_", " ").title()}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Save plot
        plot_path = os.path.join(output_dir, f'{group_name}.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Saved plot: {plot_path}")


def generate_training_report(checkpoint_dir: str, output_dir: str):
    """Generate a comprehensive training report.
    
    Args:
        checkpoint_dir: Directory containing checkpoints
        output_dir: Directory to save report
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # Load checkpoint information
    checkpoints = {}
    
    # Check for common checkpoint files
    checkpoint_files = ['best_model.pth', 'latest_model.pth']
    
    # Also check for numbered checkpoints
    if os.path.exists(checkpoint_dir):
        for file in os.listdir(checkpoint_dir):
            if file.startswith('checkpoint_epoch_') and file.endswith('.pth'):
                checkpoint_files.append(file)
    
    for checkpoint_file in checkpoint_files:
        checkpoint_path = os.path.join(checkpoint_dir, checkpoint_file)
        if os.path.exists(checkpoint_path):
            checkpoints[checkpoint_file] = load_checkpoint_metrics(checkpoint_path)
    
    # Generate report
    report_path = os.path.join(output_dir, 'training_report.txt')
    
    with open(report_path, 'w') as f:
        f.write("Image Sharpening Knowledge Distillation - Training Report\n")
        f.write("=" * 60 + "\n\n")
        
        # Checkpoint summary
        f.write("Checkpoint Summary:\n")
        f.write("-" * 20 + "\n")
        
        for checkpoint_name, info in checkpoints.items():
            if info:
                f.write(f"\n{checkpoint_name}:\n")
                f.write(f"  Epoch: {info.get('epoch', 'N/A')}\n")
                f.write(f"  Global Step: {info.get('global_step', 'N/A')}\n")
                f.write(f"  Best SSIM: {info.get('best_ssim', 'N/A'):.4f}\n")
                
                val_metrics = info.get('val_metrics', {})
                if val_metrics:
                    f.write(f"  Validation Metrics:\n")
                    for metric, value in val_metrics.items():
                        if isinstance(value, (int, float)):
                            f.write(f"    {metric}: {value:.4f}\n")
        
        # Performance analysis
        f.write(f"\n\nPerformance Analysis:\n")
        f.write("-" * 20 + "\n")
        
        best_info = checkpoints.get('best_model.pth', {})
        if best_info:
            best_ssim = best_info.get('best_ssim', 0.0)
            target_ssim = 0.90  # Default target
            
            f.write(f"Best SSIM achieved: {best_ssim:.4f}\n")
            f.write(f"Target SSIM: {target_ssim:.4f}\n")
            
            if best_ssim >= target_ssim:
                f.write("✓ Target SSIM achieved!\n")
            else:
                gap = target_ssim - best_ssim
                f.write(f"✗ Target SSIM not achieved (gap: {gap:.4f})\n")
                
                f.write("\nRecommendations:\n")
                if gap > 0.1:
                    f.write("- Consider training for more epochs\n")
                    f.write("- Try a larger student model\n")
                    f.write("- Adjust distillation parameters\n")
                elif gap > 0.05:
                    f.write("- Fine-tune learning rate\n")
                    f.write("- Adjust loss weights\n")
                else:
                    f.write("- Small gap - try longer training\n")
                    f.write("- Consider data augmentation\n")
        
        # Model information
        latest_info = checkpoints.get('latest_model.pth', {})
        if latest_info:
            f.write(f"\nTraining Progress:\n")
            f.write("-" * 20 + "\n")
            f.write(f"Latest epoch: {latest_info.get('epoch', 'N/A')}\n")
            f.write(f"Total steps: {latest_info.get('global_step', 'N/A')}\n")
    
    print(f"Training report saved: {report_path}")


def main():
    """Main monitoring function."""
    parser = argparse.ArgumentParser(description='Monitor training progress')
    parser.add_argument('--checkpoint-dir', type=str, default='results/checkpoints',
                       help='Directory containing checkpoints')
    parser.add_argument('--log-dir', type=str, default='results/logs',
                       help='Directory containing TensorBoard logs')
    parser.add_argument('--output-dir', type=str, default='results/monitoring',
                       help='Output directory for monitoring results')
    parser.add_argument('--plot-curves', action='store_true',
                       help='Generate training curve plots')
    parser.add_argument('--generate-report', action='store_true',
                       help='Generate training report')
    
    args = parser.parse_args()
    
    print("Training Progress Monitor")
    print("=" * 40)
    print(f"Checkpoint directory: {args.checkpoint_dir}")
    print(f"Log directory: {args.log_dir}")
    print(f"Output directory: {args.output_dir}")
    
    # Check if directories exist
    if not os.path.exists(args.checkpoint_dir):
        print(f"Checkpoint directory not found: {args.checkpoint_dir}")
        print("Make sure training has started and checkpoints are being saved.")
        return
    
    # Generate training report
    if args.generate_report:
        print("\nGenerating training report...")
        generate_training_report(args.checkpoint_dir, args.output_dir)
    
    # Plot training curves
    if args.plot_curves:
        print("\nParsing training logs...")
        metrics = parse_tensorboard_logs(args.log_dir)
        
        if metrics:
            print("Generating training curve plots...")
            plot_training_curves(metrics, args.output_dir)
        else:
            print("No training logs found or TensorBoard not available")
    
    # Display current status
    print("\nCurrent Training Status:")
    print("-" * 30)
    
    # Check latest checkpoint
    latest_path = os.path.join(args.checkpoint_dir, 'latest_model.pth')
    if os.path.exists(latest_path):
        latest_info = load_checkpoint_metrics(latest_path)
        if latest_info:
            print(f"Latest epoch: {latest_info.get('epoch', 'N/A')}")
            print(f"Global step: {latest_info.get('global_step', 'N/A')}")
            
            val_metrics = latest_info.get('val_metrics', {})
            if 'ssim' in val_metrics:
                print(f"Latest SSIM: {val_metrics['ssim']:.4f}")
            if 'total_loss' in val_metrics:
                print(f"Latest loss: {val_metrics['total_loss']:.4f}")
    
    # Check best checkpoint
    best_path = os.path.join(args.checkpoint_dir, 'best_model.pth')
    if os.path.exists(best_path):
        best_info = load_checkpoint_metrics(best_path)
        if best_info:
            print(f"Best SSIM: {best_info.get('best_ssim', 'N/A'):.4f}")
    
    print(f"\nMonitoring results saved to: {args.output_dir}")


if __name__ == '__main__':
    main()
