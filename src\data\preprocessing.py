"""Data preprocessing utilities for image sharpening."""

import cv2
import numpy as np
import torch
from PIL import Image
from typing import Tuple, Union, List
import random


class ImageDegradation:
    """Simulate video conferencing image degradation."""
    
    def __init__(self, 
                 downscale_methods: List[str] = ['bicubic', 'bilinear'],
                 downscale_factors: List[int] = [2, 3, 4],
                 noise_levels: List[int] = [0, 5, 10, 15],
                 compression_quality: List[int] = [70, 80, 90]):
        """Initialize degradation parameters.
        
        Args:
            downscale_methods: List of downscaling methods
            downscale_factors: List of downscaling factors
            noise_levels: List of noise levels (0-255 scale)
            compression_quality: List of JPEG compression qualities
        """
        self.downscale_methods = downscale_methods
        self.downscale_factors = downscale_factors
        self.noise_levels = noise_levels
        self.compression_quality = compression_quality
    
    def downscale_upscale(self, 
                         image: np.ndarray, 
                         method: str = 'bicubic', 
                         factor: int = 2) -> np.ndarray:
        """Downscale and upscale image to simulate quality loss.
        
        Args:
            image: Input image (H, W, C)
            method: Interpolation method ('bicubic' or 'bilinear')
            factor: Downscaling factor
            
        Returns:
            Degraded image
        """
        h, w = image.shape[:2]
        
        # Choose interpolation method
        if method == 'bicubic':
            interp_down = cv2.INTER_CUBIC
            interp_up = cv2.INTER_CUBIC
        elif method == 'bilinear':
            interp_down = cv2.INTER_LINEAR
            interp_up = cv2.INTER_LINEAR
        else:
            raise ValueError(f"Unknown interpolation method: {method}")
        
        # Downscale
        small_h, small_w = h // factor, w // factor
        downscaled = cv2.resize(image, (small_w, small_h), interpolation=interp_down)
        
        # Upscale back to original size
        upscaled = cv2.resize(downscaled, (w, h), interpolation=interp_up)
        
        return upscaled
    
    def add_noise(self, image: np.ndarray, noise_level: int = 10) -> np.ndarray:
        """Add Gaussian noise to image.
        
        Args:
            image: Input image (H, W, C) in range [0, 255]
            noise_level: Noise standard deviation
            
        Returns:
            Noisy image
        """
        if noise_level == 0:
            return image
            
        noise = np.random.normal(0, noise_level, image.shape)
        noisy_image = image + noise
        noisy_image = np.clip(noisy_image, 0, 255)
        
        return noisy_image.astype(np.uint8)
    
    def jpeg_compression(self, image: np.ndarray, quality: int = 80) -> np.ndarray:
        """Apply JPEG compression to image.
        
        Args:
            image: Input image (H, W, C)
            quality: JPEG quality (1-100)
            
        Returns:
            Compressed image
        """
        # Convert to PIL Image
        if image.dtype != np.uint8:
            image = (image * 255).astype(np.uint8)
            
        pil_image = Image.fromarray(image)
        
        # Apply JPEG compression
        import io
        buffer = io.BytesIO()
        pil_image.save(buffer, format='JPEG', quality=quality)
        buffer.seek(0)
        compressed_image = Image.open(buffer)
        
        return np.array(compressed_image)
    
    def apply_random_degradation(self, image: np.ndarray) -> np.ndarray:
        """Apply random degradation to simulate video conferencing conditions.
        
        Args:
            image: Input image (H, W, C) in range [0, 255]
            
        Returns:
            Degraded image
        """
        degraded = image.copy()
        
        # Random downscale-upscale
        method = random.choice(self.downscale_methods)
        factor = random.choice(self.downscale_factors)
        degraded = self.downscale_upscale(degraded, method, factor)
        
        # Random noise
        noise_level = random.choice(self.noise_levels)
        degraded = self.add_noise(degraded, noise_level)
        
        # Random JPEG compression
        quality = random.choice(self.compression_quality)
        degraded = self.jpeg_compression(degraded, quality)
        
        return degraded


class ImagePreprocessor:
    """Image preprocessing for training and inference."""
    
    def __init__(self, 
                 input_size: Tuple[int, int] = (256, 256),
                 normalize: bool = True):
        """Initialize preprocessor.
        
        Args:
            input_size: Target input size (H, W)
            normalize: Whether to normalize to [0, 1] range
        """
        self.input_size = input_size
        self.normalize = normalize
        self.degradation = ImageDegradation()
    
    def load_image(self, image_path: str) -> np.ndarray:
        """Load image from file.
        
        Args:
            image_path: Path to image file
            
        Returns:
            Image array (H, W, C)
        """
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not load image: {image_path}")
            
        # Convert BGR to RGB
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        return image
    
    def resize_image(self, 
                    image: np.ndarray, 
                    size: Tuple[int, int],
                    keep_aspect_ratio: bool = False) -> np.ndarray:
        """Resize image to target size.
        
        Args:
            image: Input image (H, W, C)
            size: Target size (H, W)
            keep_aspect_ratio: Whether to maintain aspect ratio
            
        Returns:
            Resized image
        """
        if keep_aspect_ratio:
            h, w = image.shape[:2]
            target_h, target_w = size
            
            # Calculate scaling factor
            scale = min(target_h / h, target_w / w)
            new_h, new_w = int(h * scale), int(w * scale)
            
            # Resize image
            resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_CUBIC)
            
            # Pad to target size
            pad_h = (target_h - new_h) // 2
            pad_w = (target_w - new_w) // 2
            
            padded = np.pad(resized, 
                           ((pad_h, target_h - new_h - pad_h),
                            (pad_w, target_w - new_w - pad_w),
                            (0, 0)), 
                           mode='constant', constant_values=0)
            
            return padded
        else:
            return cv2.resize(image, (size[1], size[0]), interpolation=cv2.INTER_CUBIC)
    
    def random_crop(self, 
                   image: np.ndarray, 
                   crop_size: Tuple[int, int]) -> np.ndarray:
        """Randomly crop image to specified size.
        
        Args:
            image: Input image (H, W, C)
            crop_size: Crop size (H, W)
            
        Returns:
            Cropped image
        """
        h, w = image.shape[:2]
        crop_h, crop_w = crop_size
        
        if h < crop_h or w < crop_w:
            # Pad if image is smaller than crop size
            pad_h = max(0, crop_h - h)
            pad_w = max(0, crop_w - w)
            image = np.pad(image, 
                          ((0, pad_h), (0, pad_w), (0, 0)), 
                          mode='reflect')
            h, w = image.shape[:2]
        
        # Random crop
        start_h = random.randint(0, h - crop_h)
        start_w = random.randint(0, w - crop_w)
        
        cropped = image[start_h:start_h + crop_h, start_w:start_w + crop_w]
        
        return cropped
    
    def to_tensor(self, image: np.ndarray) -> torch.Tensor:
        """Convert numpy array to PyTorch tensor.
        
        Args:
            image: Input image (H, W, C) in range [0, 255]
            
        Returns:
            Tensor (C, H, W) in range [0, 1] if normalize=True
        """
        # Convert to float and normalize
        if self.normalize:
            image = image.astype(np.float32) / 255.0
        
        # Convert HWC to CHW
        tensor = torch.from_numpy(image.transpose(2, 0, 1))
        
        return tensor
    
    def preprocess_pair(self, 
                       hr_image: np.ndarray,
                       apply_degradation: bool = True) -> Tuple[torch.Tensor, torch.Tensor]:
        """Preprocess high-resolution image and create LR-HR pair.
        
        Args:
            hr_image: High-resolution image (H, W, C)
            apply_degradation: Whether to apply degradation for LR image
            
        Returns:
            Tuple of (LR tensor, HR tensor)
        """
        # Resize to input size for training efficiency
        hr_resized = self.resize_image(hr_image, self.input_size)
        
        # Create degraded version
        if apply_degradation:
            lr_image = self.degradation.apply_random_degradation(hr_resized)
        else:
            lr_image = hr_resized.copy()
        
        # Convert to tensors
        lr_tensor = self.to_tensor(lr_image)
        hr_tensor = self.to_tensor(hr_resized)
        
        return lr_tensor, hr_tensor


class ImageDataset(torch.utils.data.Dataset):
    """Dataset class for image sharpening."""

    def __init__(self,
                 data_dir: str,
                 input_size: Tuple[int, int] = (256, 256),
                 apply_degradation: bool = True,
                 augment: bool = True):
        """Initialize dataset.

        Args:
            data_dir: Directory containing images
            input_size: Input image size
            apply_degradation: Whether to apply degradation
            augment: Whether to apply data augmentation
        """
        self.data_dir = data_dir
        self.apply_degradation = apply_degradation
        self.augment = augment

        self.preprocessor = ImagePreprocessor(input_size)

        # Get list of image files
        import os
        self.image_files = []
        for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
            self.image_files.extend([
                os.path.join(data_dir, f)
                for f in os.listdir(data_dir)
                if f.lower().endswith(ext)
            ])

        if len(self.image_files) == 0:
            raise ValueError(f"No images found in {data_dir}")

    def __len__(self) -> int:
        """Return dataset size."""
        return len(self.image_files)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """Get item from dataset.

        Args:
            idx: Item index

        Returns:
            Tuple of (LR image, HR image)
        """
        # Load image
        image_path = self.image_files[idx]
        image = self.preprocessor.load_image(image_path)

        # Apply augmentation if enabled
        if self.augment:
            image = self._apply_augmentation(image)

        # Preprocess to create LR-HR pair
        lr_tensor, hr_tensor = self.preprocessor.preprocess_pair(
            image, self.apply_degradation
        )

        return lr_tensor, hr_tensor

    def _apply_augmentation(self, image: np.ndarray) -> np.ndarray:
        """Apply data augmentation.

        Args:
            image: Input image

        Returns:
            Augmented image
        """
        # Random horizontal flip
        if random.random() > 0.5:
            image = cv2.flip(image, 1)

        # Random vertical flip
        if random.random() > 0.5:
            image = cv2.flip(image, 0)

        # Random rotation (90, 180, 270 degrees)
        if random.random() > 0.5:
            k = random.randint(1, 3)
            image = np.rot90(image, k)

        return image
