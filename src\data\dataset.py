"""Dataset utilities for image sharpening training."""

import torch
from torch.utils.data import DataLoader, Dataset
import os
from typing import Tuple, List, Optional
import random
from .preprocessing import ImagePreprocessor
import numpy as np


class SharpnessDataset(Dataset):
    """Dataset for image sharpening with knowledge distillation."""
    
    def __init__(self, 
                 data_dir: str,
                 input_size: Tuple[int, int] = (256, 256),
                 apply_degradation: bool = True,
                 augment: bool = True,
                 file_extensions: List[str] = None):
        """Initialize dataset.
        
        Args:
            data_dir: Directory containing images
            input_size: Input image size for training
            apply_degradation: Whether to apply degradation
            augment: Whether to apply data augmentation
            file_extensions: List of supported file extensions
        """
        self.data_dir = data_dir
        self.apply_degradation = apply_degradation
        self.augment = augment
        
        if file_extensions is None:
            file_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        
        self.preprocessor = ImagePreprocessor(input_size)
        
        # Get list of image files
        self.image_files = self._get_image_files(data_dir, file_extensions)
        
        if len(self.image_files) == 0:
            raise ValueError(f"No images found in {data_dir}")
        
        print(f"Found {len(self.image_files)} images in {data_dir}")
    
    def _get_image_files(self, data_dir: str, extensions: List[str]) -> List[str]:
        """Get list of image files in directory.
        
        Args:
            data_dir: Directory to search
            extensions: List of file extensions to include
            
        Returns:
            List of image file paths
        """
        image_files = []
        
        if not os.path.exists(data_dir):
            print(f"Warning: Directory {data_dir} does not exist")
            return image_files
        
        for root, dirs, files in os.walk(data_dir):
            for file in files:
                if any(file.lower().endswith(ext) for ext in extensions):
                    image_files.append(os.path.join(root, file))
        
        return sorted(image_files)
    
    def __len__(self) -> int:
        """Return dataset size."""
        return len(self.image_files)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """Get item from dataset.
        
        Args:
            idx: Item index
            
        Returns:
            Tuple of (LR image, HR image)
        """
        try:
            # Load image
            image_path = self.image_files[idx]
            image = self.preprocessor.load_image(image_path)
            
            # Apply augmentation if enabled
            if self.augment:
                image = self._apply_augmentation(image)
            
            # Preprocess to create LR-HR pair
            lr_tensor, hr_tensor = self.preprocessor.preprocess_pair(
                image, self.apply_degradation
            )
            
            return lr_tensor, hr_tensor
            
        except Exception as e:
            print(f"Error loading image {self.image_files[idx]}: {e}")
            # Return a random other image if this one fails
            new_idx = random.randint(0, len(self.image_files) - 1)
            return self.__getitem__(new_idx)
    
    def _apply_augmentation(self, image: np.ndarray) -> np.ndarray:
        """Apply data augmentation.
        
        Args:
            image: Input image
            
        Returns:
            Augmented image
        """
        import cv2
        
        # Random horizontal flip
        if random.random() > 0.5:
            image = cv2.flip(image, 1)
        
        # Random vertical flip
        if random.random() > 0.5:
            image = cv2.flip(image, 0)
        
        # Random rotation (90, 180, 270 degrees)
        if random.random() > 0.5:
            k = random.randint(1, 3)
            image = np.rot90(image, k)
        
        # Random brightness adjustment
        if random.random() > 0.5:
            brightness_factor = random.uniform(0.8, 1.2)
            image = np.clip(image * brightness_factor, 0, 255).astype(np.uint8)
        
        # Random contrast adjustment
        if random.random() > 0.5:
            contrast_factor = random.uniform(0.8, 1.2)
            mean = image.mean()
            image = np.clip((image - mean) * contrast_factor + mean, 0, 255).astype(np.uint8)
        
        return image


class BenchmarkDataset(Dataset):
    """Dataset for benchmark evaluation with diverse categories."""
    
    def __init__(self, 
                 data_dir: str,
                 categories: List[str] = None,
                 min_images_per_category: int = 20):
        """Initialize benchmark dataset.
        
        Args:
            data_dir: Directory containing benchmark images
            categories: List of image categories
            min_images_per_category: Minimum images required per category
        """
        self.data_dir = data_dir
        
        if categories is None:
            categories = ["text", "nature", "people", "animals", "games"]
        
        self.categories = categories
        self.min_images_per_category = min_images_per_category
        
        self.preprocessor = ImagePreprocessor(normalize=True)
        
        # Organize images by category
        self.category_images = self._organize_by_category()
        
        # Create flat list of all images
        self.all_images = []
        for category, images in self.category_images.items():
            self.all_images.extend([(img, category) for img in images])
        
        print(f"Benchmark dataset loaded: {len(self.all_images)} images")
        for category, images in self.category_images.items():
            print(f"  {category}: {len(images)} images")
    
    def _organize_by_category(self) -> dict:
        """Organize images by category directories.
        
        Returns:
            Dictionary mapping category names to image file lists
        """
        category_images = {}
        
        for category in self.categories:
            category_dir = os.path.join(self.data_dir, category)
            if os.path.exists(category_dir):
                images = []
                for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                    images.extend([
                        os.path.join(category_dir, f)
                        for f in os.listdir(category_dir)
                        if f.lower().endswith(ext)
                    ])
                
                if len(images) >= self.min_images_per_category:
                    category_images[category] = images
                else:
                    print(f"Warning: Category '{category}' has only {len(images)} images "
                          f"(minimum required: {self.min_images_per_category})")
            else:
                print(f"Warning: Category directory '{category_dir}' not found")
        
        return category_images
    
    def __len__(self) -> int:
        """Return dataset size."""
        return len(self.all_images)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, str]:
        """Get item from dataset.
        
        Args:
            idx: Item index
            
        Returns:
            Tuple of (image tensor, category)
        """
        image_path, category = self.all_images[idx]
        
        try:
            # Load and preprocess image
            image = self.preprocessor.load_image(image_path)
            
            # Convert to tensor without degradation
            tensor = self.preprocessor.to_tensor(image)
            
            return tensor, category
            
        except Exception as e:
            print(f"Error loading benchmark image {image_path}: {e}")
            # Return a random other image if this one fails
            new_idx = random.randint(0, len(self.all_images) - 1)
            return self.__getitem__(new_idx)


def create_data_loaders(config, 
                       train_dir: str,
                       val_dir: str,
                       test_dir: Optional[str] = None) -> Tuple[DataLoader, DataLoader, Optional[DataLoader]]:
    """Create data loaders for training, validation, and testing.
    
    Args:
        config: Configuration object
        train_dir: Training data directory
        val_dir: Validation data directory
        test_dir: Test data directory (optional)
        
    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    # Training dataset with augmentation and degradation
    train_dataset = SharpnessDataset(
        data_dir=train_dir,
        input_size=config.get('dataset.input_size', [256, 256]),
        apply_degradation=True,
        augment=True
    )
    
    # Validation dataset without augmentation but with degradation
    val_dataset = SharpnessDataset(
        data_dir=val_dir,
        input_size=config.get('dataset.input_size', [256, 256]),
        apply_degradation=True,
        augment=False
    )
    
    # Test dataset (if provided)
    test_dataset = None
    if test_dir and os.path.exists(test_dir):
        test_dataset = SharpnessDataset(
            data_dir=test_dir,
            input_size=config.get('dataset.input_size', [256, 256]),
            apply_degradation=True,
            augment=False
        )
    
    # Create data loaders
    batch_size = config.get('training.batch_size', 16)
    num_workers = config.get('hardware.num_workers', 4)
    pin_memory = config.get('hardware.pin_memory', True)
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=pin_memory,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory
    )
    
    test_loader = None
    if test_dataset:
        test_loader = DataLoader(
            test_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=pin_memory
        )
    
    return train_loader, val_loader, test_loader
