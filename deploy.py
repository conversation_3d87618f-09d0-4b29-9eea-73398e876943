#!/usr/bin/env python3
"""Deployment script for Image Sharpening Knowledge Distillation project."""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path
import argparse


def create_deployment_package(model_path: str, output_dir: str, include_examples: bool = True):
    """Create a deployment package with the trained model.
    
    Args:
        model_path: Path to the trained model
        output_dir: Output directory for deployment package
        include_examples: Whether to include example scripts
    """
    print(f"Creating deployment package...")
    print(f"Model: {model_path}")
    print(f"Output: {output_dir}")
    
    # Create deployment directory structure
    deploy_dir = Path(output_dir)
    deploy_dir.mkdir(parents=True, exist_ok=True)
    
    # Core directories
    (deploy_dir / "models").mkdir(exist_ok=True)
    (deploy_dir / "src").mkdir(exist_ok=True)
    (deploy_dir / "examples").mkdir(exist_ok=True)
    (deploy_dir / "docs").mkdir(exist_ok=True)
    
    # Copy model
    if os.path.exists(model_path):
        shutil.copy2(model_path, deploy_dir / "models" / "sharpening_model.pth")
        print("✓ Copied trained model")
    else:
        print(f"⚠ Model not found: {model_path}")
    
    # Copy essential source files
    essential_files = [
        "src/models/student_models.py",
        "src/inference/optimized_inference.py",
        "src/utils/metrics.py",
        "src/utils/config.py"
    ]
    
    for file_path in essential_files:
        if os.path.exists(file_path):
            dest_path = deploy_dir / file_path
            dest_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(file_path, dest_path)
            print(f"✓ Copied {file_path}")
    
    # Create deployment requirements
    deploy_requirements = [
        "torch>=1.9.0",
        "torchvision>=0.10.0",
        "opencv-python>=4.5.0",
        "numpy>=1.21.0",
        "pillow>=8.0.0",
        "onnxruntime>=1.8.0",  # For ONNX inference
        "pyyaml>=5.4.0"
    ]
    
    with open(deploy_dir / "requirements.txt", "w") as f:
        f.write("\n".join(deploy_requirements))
    print("✓ Created deployment requirements.txt")
    
    # Create simple inference example
    inference_example = '''#!/usr/bin/env python3
"""Simple inference example for image sharpening."""

import cv2
import sys
import os

# Add src to path
sys.path.append('src')

from inference.optimized_inference import OptimizedInferenceEngine


def main():
    """Main inference function."""
    if len(sys.argv) != 3:
        print("Usage: python inference_example.py <input_image> <output_image>")
        return
    
    input_path = sys.argv[1]
    output_path = sys.argv[2]
    
    # Check input file
    if not os.path.exists(input_path):
        print(f"Error: Input file not found: {input_path}")
        return
    
    # Initialize inference engine
    model_path = "models/sharpening_model.pth"
    engine = OptimizedInferenceEngine(model_path, device='cuda')
    
    # Load image
    image = cv2.imread(input_path)
    if image is None:
        print(f"Error: Could not load image: {input_path}")
        return
    
    # Run inference
    enhanced_image, inference_time = engine.infer_image(image)
    
    # Save result
    cv2.imwrite(output_path, enhanced_image)
    
    print(f"Image enhanced successfully!")
    print(f"Input: {input_path}")
    print(f"Output: {output_path}")
    print(f"Inference time: {inference_time*1000:.2f}ms")
    print(f"FPS: {1.0/inference_time:.1f}")


if __name__ == "__main__":
    main()
'''
    
    if include_examples:
        with open(deploy_dir / "examples" / "inference_example.py", "w") as f:
            f.write(inference_example)
        print("✓ Created inference example")
    
    # Create deployment README
    deploy_readme = f'''# Image Sharpening Model - Deployment Package

This package contains a trained image sharpening model optimized for real-time video conferencing applications.

## Quick Start

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run inference on an image:
   ```bash
   python examples/inference_example.py input.jpg output.jpg
   ```

## Model Information

- **Model Type**: Lightweight CNN for real-time image sharpening
- **Target Performance**: 30-60 FPS on 1920x1080 resolution
- **Target Quality**: SSIM > 90%
- **Model Size**: < 10MB
- **Parameters**: < 1M

## Files

- `models/sharpening_model.pth`: Trained PyTorch model
- `src/`: Essential source code for inference
- `examples/`: Usage examples
- `requirements.txt`: Python dependencies

## Usage

### Python API

```python
from src.inference.optimized_inference import OptimizedInferenceEngine

# Initialize engine
engine = OptimizedInferenceEngine("models/sharpening_model.pth", device="cuda")

# Process image
enhanced_image, inference_time = engine.infer_image(input_image)
```

### Performance Optimization

For maximum performance:
1. Use CUDA if available
2. Consider ONNX conversion for deployment
3. Use batch processing for multiple images

## Support

For questions and issues, please refer to the main project repository.
'''
    
    with open(deploy_dir / "README.md", "w") as f:
        f.write(deploy_readme)
    print("✓ Created deployment README")
    
    # Create deployment info
    deploy_info = {
        "package_version": "1.0.0",
        "model_file": "models/sharpening_model.pth",
        "created_by": "Image Sharpening Knowledge Distillation Project",
        "target_performance": {
            "fps": "30-60",
            "resolution": "1920x1080",
            "ssim": ">90%"
        },
        "requirements": deploy_requirements
    }
    
    with open(deploy_dir / "deployment_info.json", "w") as f:
        json.dump(deploy_info, f, indent=2)
    print("✓ Created deployment info")
    
    print(f"\n🎉 Deployment package created successfully!")
    print(f"Location: {deploy_dir}")
    print(f"Package size: {get_directory_size(deploy_dir):.2f} MB")


def get_directory_size(path: Path) -> float:
    """Get directory size in MB."""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(path):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            total_size += os.path.getsize(filepath)
    return total_size / (1024 * 1024)


def create_docker_deployment(model_path: str, output_dir: str):
    """Create Docker deployment files.
    
    Args:
        model_path: Path to trained model
        output_dir: Output directory
    """
    print("Creating Docker deployment files...")
    
    deploy_dir = Path(output_dir)
    
    # Dockerfile
    dockerfile_content = '''FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    libglib2.0-0 \\
    libsm6 \\
    libxext6 \\
    libxrender-dev \\
    libgomp1 \\
    libglib2.0-0 \\
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application files
COPY src/ src/
COPY models/ models/
COPY examples/ examples/

# Expose port for API (if needed)
EXPOSE 8000

# Default command
CMD ["python", "examples/inference_example.py"]
'''
    
    with open(deploy_dir / "Dockerfile", "w") as f:
        f.write(dockerfile_content)
    
    # Docker compose
    docker_compose_content = '''version: '3.8'

services:
  image-sharpening:
    build: .
    volumes:
      - ./input:/app/input
      - ./output:/app/output
    environment:
      - CUDA_VISIBLE_DEVICES=0
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
'''
    
    with open(deploy_dir / "docker-compose.yml", "w") as f:
        f.write(docker_compose_content)
    
    print("✓ Created Docker deployment files")


def main():
    """Main deployment function."""
    parser = argparse.ArgumentParser(description='Deploy image sharpening model')
    parser.add_argument('--model-path', type=str, required=True,
                       help='Path to trained model')
    parser.add_argument('--output-dir', type=str, default='deployment',
                       help='Output directory for deployment package')
    parser.add_argument('--include-docker', action='store_true',
                       help='Include Docker deployment files')
    parser.add_argument('--include-examples', action='store_true', default=True,
                       help='Include example scripts')
    
    args = parser.parse_args()
    
    print("Image Sharpening Model - Deployment")
    print("=" * 50)
    
    # Check model exists
    if not os.path.exists(args.model_path):
        print(f"Error: Model not found at {args.model_path}")
        return
    
    # Create deployment package
    create_deployment_package(
        model_path=args.model_path,
        output_dir=args.output_dir,
        include_examples=args.include_examples
    )
    
    # Create Docker files if requested
    if args.include_docker:
        create_docker_deployment(args.model_path, args.output_dir)
    
    print(f"\n📦 Deployment package ready!")
    print(f"To use the package:")
    print(f"1. cd {args.output_dir}")
    print(f"2. pip install -r requirements.txt")
    print(f"3. python examples/inference_example.py input.jpg output.jpg")
    
    if args.include_docker:
        print(f"\nDocker deployment:")
        print(f"1. cd {args.output_dir}")
        print(f"2. docker build -t image-sharpening .")
        print(f"3. docker run -v $(pwd)/input:/app/input -v $(pwd)/output:/app/output image-sharpening")


if __name__ == "__main__":
    main()
