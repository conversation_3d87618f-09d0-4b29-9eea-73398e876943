"""Main training script for knowledge distillation."""

import os
import argparse
import torch
import sys
from pathlib import Path

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.training.trainer import KnowledgeDistillationTrainer
from src.data.dataset import create_data_loaders
from src.utils.config import load_config


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description='Train image sharpening model with knowledge distillation')
    parser.add_argument('--config', type=str, default='config.yaml',
                       help='Path to configuration file')
    parser.add_argument('--resume', type=str, default=None,
                       help='Path to checkpoint to resume from')
    parser.add_argument('--device', type=str, default=None,
                       choices=['cpu', 'cuda'], help='Device to use (overrides config)')
    parser.add_argument('--epochs', type=int, default=None,
                       help='Number of epochs (overrides config)')
    parser.add_argument('--batch-size', type=int, default=None,
                       help='Batch size (overrides config)')
    parser.add_argument('--learning-rate', type=float, default=None,
                       help='Learning rate (overrides config)')
    parser.add_argument('--data-root', type=str, default='data',
                       help='Root directory for datasets')
    
    args = parser.parse_args()
    
    # Load configuration
    try:
        config = load_config(args.config)
    except Exception as e:
        print(f"Error loading config: {e}")
        return
    
    # Override config with command line arguments
    if args.device:
        config.set('hardware.device', args.device)
    if args.epochs:
        config.set('training.num_epochs', args.epochs)
    if args.batch_size:
        config.set('training.batch_size', args.batch_size)
    if args.learning_rate:
        config.set('training.learning_rate', args.learning_rate)
    
    # Set device
    device = config.get('hardware.device', 'cuda')
    if device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        device = 'cpu'
        config.set('hardware.device', device)
    
    print("Knowledge Distillation Training")
    print("=" * 50)
    print(f"Config file: {args.config}")
    print(f"Device: {device}")
    print(f"Data root: {args.data_root}")
    
    # Check if datasets exist
    data_root = Path(args.data_root)
    train_dir = data_root / 'train'
    val_dir = data_root / 'val'
    
    if not train_dir.exists() or not val_dir.exists():
        print(f"Error: Training or validation data not found!")
        print(f"Expected directories:")
        print(f"  Training: {train_dir}")
        print(f"  Validation: {val_dir}")
        print("\nPlease run the dataset preparation script first:")
        print("  python scripts/prepare_dataset.py --create-samples")
        return
    
    # Create data loaders
    print("\nCreating data loaders...")
    try:
        train_loader, val_loader, _ = create_data_loaders(
            config=config,
            train_dir=str(train_dir),
            val_dir=str(val_dir)
        )
        
        print(f"Training samples: {len(train_loader.dataset)}")
        print(f"Validation samples: {len(val_loader.dataset)}")
        print(f"Batch size: {train_loader.batch_size}")
        
    except Exception as e:
        print(f"Error creating data loaders: {e}")
        return
    
    # Check teacher model
    teacher_config = config.get('model.teacher', {})
    teacher_path = teacher_config.get('pretrained_path', '')
    
    if teacher_path and not os.path.exists(teacher_path):
        print(f"\nWarning: Teacher model not found at {teacher_path}")
        print("Please run the teacher model setup script first:")
        print("  python scripts/setup_teacher_models.py --create-dummy")
        print("Continuing with randomly initialized teacher model...")
    
    # Initialize trainer
    print("\nInitializing trainer...")
    try:
        trainer = KnowledgeDistillationTrainer(config, device)
    except Exception as e:
        print(f"Error initializing trainer: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Resume from checkpoint if specified
    if args.resume:
        if os.path.exists(args.resume):
            print(f"Resuming from checkpoint: {args.resume}")
            trainer.load_checkpoint(args.resume)
        else:
            print(f"Checkpoint not found: {args.resume}")
            return
    
    # Print training configuration
    print("\nTraining Configuration:")
    print("-" * 30)
    print(f"Teacher model: {config.get('model.teacher.name', 'edsr')}")
    print(f"Student model: {config.get('model.student.name', 'lightweightsr')}")
    print(f"Epochs: {config.get('training.num_epochs', 100)}")
    print(f"Learning rate: {config.get('training.learning_rate', 1e-4)}")
    print(f"Optimizer: {config.get('training.optimizer', 'adam')}")
    print(f"Scheduler: {config.get('training.scheduler', 'cosine')}")
    print(f"Distillation alpha: {config.get('training.distillation.alpha', 0.7)}")
    print(f"Task loss beta: {config.get('training.distillation.beta', 0.3)}")
    print(f"Target SSIM: {config.get('evaluation.target_ssim', 0.90)}")
    
    # Start training
    print("\nStarting training...")
    print("=" * 50)
    
    try:
        trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            num_epochs=args.epochs
        )
        
        print("\n" + "=" * 50)
        print("Training completed successfully!")
        
        # Print final results
        print(f"Best SSIM achieved: {trainer.best_ssim:.4f}")
        
        target_ssim = config.get('evaluation.target_ssim', 0.90)
        if trainer.best_ssim >= target_ssim:
            print(f"✓ Target SSIM {target_ssim:.3f} achieved!")
        else:
            print(f"✗ Target SSIM {target_ssim:.3f} not achieved")
            print("Consider:")
            print("  - Training for more epochs")
            print("  - Adjusting learning rate")
            print("  - Using a different student model architecture")
            print("  - Tuning distillation parameters")
        
        # Print model location
        checkpoint_dir = config.get('training.checkpoint_dir', 'results/checkpoints')
        print(f"\nTrained models saved in: {checkpoint_dir}")
        print("  - best_model.pth: Best performing model")
        print("  - latest_model.pth: Latest model")
        
        print("\nNext steps:")
        print("1. Evaluate the model:")
        print("   python scripts/evaluate_model.py --model-path results/checkpoints/best_model.pth")
        print("2. Test inference speed:")
        print("   python scripts/test_student_models.py")
        print("3. Optimize for deployment:")
        print("   python scripts/optimize_student_model.py")
        
    except KeyboardInterrupt:
        print("\nTraining interrupted by user")
        
        # Save current state
        checkpoint_dir = config.get('training.checkpoint_dir', 'results/checkpoints')
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        interrupted_path = os.path.join(checkpoint_dir, 'interrupted_model.pth')
        trainer.save_checkpoint(trainer.current_epoch, {}, False)
        
        print(f"Model state saved to: {interrupted_path}")
        print("You can resume training with:")
        print(f"  python scripts/train_knowledge_distillation.py --resume {interrupted_path}")
        
    except Exception as e:
        print(f"\nTraining failed with error: {e}")
        import traceback
        traceback.print_exc()
        
        # Try to save current state
        try:
            checkpoint_dir = config.get('training.checkpoint_dir', 'results/checkpoints')
            os.makedirs(checkpoint_dir, exist_ok=True)
            
            error_path = os.path.join(checkpoint_dir, 'error_model.pth')
            trainer.save_checkpoint(trainer.current_epoch, {}, False)
            print(f"Model state saved to: {error_path}")
        except:
            print("Could not save model state")


if __name__ == '__main__':
    main()
