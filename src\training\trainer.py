"""Knowledge distillation trainer for image sharpening."""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import os
import time
from typing import Dict, Optional, Tuple
from tqdm import tqdm
import wandb

from .losses import CombinedLoss
from ..models.teacher_models import TeacherModelManager
from ..models.student_models import StudentModelFactory
from ..utils.metrics import ImageMetrics


class KnowledgeDistillationTrainer:
    """Trainer for knowledge distillation of image sharpening models."""
    
    def __init__(self,
                 config: dict,
                 device: str = 'cuda'):
        """Initialize trainer.
        
        Args:
            config: Training configuration
            device: Device for training
        """
        self.config = config
        self.device = device
        
        # Initialize models
        self._setup_models()
        
        # Initialize loss function
        self._setup_loss()
        
        # Initialize optimizer and scheduler
        self._setup_optimizer()
        
        # Initialize metrics
        self.metrics = ImageMetrics(device)
        
        # Initialize logging
        self._setup_logging()
        
        # Training state
        self.current_epoch = 0
        self.global_step = 0
        self.best_ssim = 0.0
        
    def _setup_models(self):
        """Setup teacher and student models."""
        # Teacher model
        teacher_config = self.config.get('model.teacher', {})
        teacher_name = teacher_config.get('name', 'edsr')
        teacher_path = teacher_config.get('pretrained_path', '')
        
        self.teacher_manager = TeacherModelManager(teacher_name, self.device)
        self.teacher_model = self.teacher_manager.load_pretrained(teacher_path)
        
        # Freeze teacher model
        for param in self.teacher_model.parameters():
            param.requires_grad = False
        
        # Student model
        student_config = self.config.get('model.student', {})
        student_name = student_config.get('name', 'lightweightsr')
        
        self.student_model = StudentModelFactory.create_model(
            student_name, **student_config
        ).to(self.device)
        
        print(f"Teacher model: {teacher_name}")
        print(f"Student model: {student_name}")
        print(f"Student parameters: {sum(p.numel() for p in self.student_model.parameters()):,}")
    
    def _setup_loss(self):
        """Setup loss function."""
        loss_config = self.config.get('training.losses', {})
        distill_config = self.config.get('training.distillation', {})
        
        # Combine loss configurations
        combined_config = {**loss_config, **distill_config}
        
        self.criterion = CombinedLoss(combined_config, self.device)
    
    def _setup_optimizer(self):
        """Setup optimizer and scheduler."""
        training_config = self.config.get('training', {})
        
        # Optimizer
        optimizer_type = training_config.get('optimizer', 'adam').lower()
        lr = training_config.get('learning_rate', 1e-4)
        weight_decay = training_config.get('weight_decay', 1e-4)
        
        if optimizer_type == 'adam':
            self.optimizer = optim.Adam(
                self.student_model.parameters(),
                lr=lr,
                weight_decay=weight_decay
            )
        elif optimizer_type == 'adamw':
            self.optimizer = optim.AdamW(
                self.student_model.parameters(),
                lr=lr,
                weight_decay=weight_decay
            )
        elif optimizer_type == 'sgd':
            self.optimizer = optim.SGD(
                self.student_model.parameters(),
                lr=lr,
                momentum=0.9,
                weight_decay=weight_decay
            )
        else:
            self.optimizer = optim.Adam(
                self.student_model.parameters(),
                lr=lr,
                weight_decay=weight_decay
            )
        
        # Scheduler
        scheduler_type = training_config.get('scheduler', 'cosine').lower()
        num_epochs = training_config.get('num_epochs', 100)
        
        if scheduler_type == 'cosine':
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer, T_max=num_epochs
            )
        elif scheduler_type == 'step':
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer, step_size=30, gamma=0.1
            )
        elif scheduler_type == 'plateau':
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer, mode='max', factor=0.5, patience=10
            )
        else:
            self.scheduler = None
    
    def _setup_logging(self):
        """Setup logging and monitoring."""
        logging_config = self.config.get('logging', {})
        
        # TensorBoard
        log_dir = os.path.join('results', 'logs', f'run_{int(time.time())}')
        self.writer = SummaryWriter(log_dir)
        
        # Weights & Biases
        if logging_config.get('use_wandb', False):
            wandb.init(
                project=logging_config.get('project_name', 'image-sharpening-kd'),
                config=self.config
            )
            self.use_wandb = True
        else:
            self.use_wandb = False
        
        self.log_every = logging_config.get('log_every', 100)
        self.save_images = logging_config.get('save_images', True)
        self.num_sample_images = logging_config.get('num_sample_images', 8)
    
    def train_epoch(self, train_loader: DataLoader) -> Dict[str, float]:
        """Train for one epoch.
        
        Args:
            train_loader: Training data loader
            
        Returns:
            Dictionary of training metrics
        """
        self.student_model.train()
        self.teacher_model.eval()
        
        epoch_losses = {}
        num_batches = len(train_loader)
        
        pbar = tqdm(train_loader, desc=f'Epoch {self.current_epoch}')
        
        for batch_idx, (lr_images, hr_images) in enumerate(pbar):
            lr_images = lr_images.to(self.device)
            hr_images = hr_images.to(self.device)
            
            # Forward pass through student model
            student_output = self.student_model(lr_images)
            
            # Forward pass through teacher model (no gradients)
            with torch.no_grad():
                teacher_output = self.teacher_model(lr_images)
                teacher_features = self.teacher_manager.get_intermediate_features(lr_images)
            
            # Get student features (if needed for feature distillation)
            student_features = None  # TODO: Implement feature extraction for student
            
            # Calculate loss
            losses = self.criterion(
                student_output=student_output,
                teacher_output=teacher_output,
                target=hr_images,
                student_features=student_features,
                teacher_features=teacher_features
            )
            
            # Backward pass
            self.optimizer.zero_grad()
            losses['total_loss'].backward()
            self.optimizer.step()
            
            # Update metrics
            for key, value in losses.items():
                if key not in epoch_losses:
                    epoch_losses[key] = 0.0
                epoch_losses[key] += value.item()
            
            # Update progress bar
            pbar.set_postfix({
                'loss': losses['total_loss'].item(),
                'task': losses['task_loss'].item(),
                'distill': losses['distillation_loss'].item()
            })
            
            # Log to tensorboard/wandb
            if self.global_step % self.log_every == 0:
                self._log_training_step(losses, lr_images, student_output, hr_images)
            
            self.global_step += 1
        
        # Average losses over epoch
        for key in epoch_losses:
            epoch_losses[key] /= num_batches
        
        return epoch_losses
    
    def validate(self, val_loader: DataLoader) -> Dict[str, float]:
        """Validate the model.
        
        Args:
            val_loader: Validation data loader
            
        Returns:
            Dictionary of validation metrics
        """
        self.student_model.eval()
        self.teacher_model.eval()
        
        val_losses = {}
        val_metrics = {'ssim': 0.0, 'psnr': 0.0, 'lpips': 0.0}
        num_batches = len(val_loader)
        
        with torch.no_grad():
            for lr_images, hr_images in tqdm(val_loader, desc='Validation'):
                lr_images = lr_images.to(self.device)
                hr_images = hr_images.to(self.device)
                
                # Forward pass
                student_output = self.student_model(lr_images)
                teacher_output = self.teacher_model(lr_images)
                teacher_features = self.teacher_manager.get_intermediate_features(lr_images)
                
                # Calculate loss
                losses = self.criterion(
                    student_output=student_output,
                    teacher_output=teacher_output,
                    target=hr_images,
                    teacher_features=teacher_features
                )
                
                # Update losses
                for key, value in losses.items():
                    if key not in val_losses:
                        val_losses[key] = 0.0
                    val_losses[key] += value.item()
                
                # Calculate metrics
                batch_metrics = self.metrics.calculate_all_metrics(student_output, hr_images)
                for key, value in batch_metrics.items():
                    val_metrics[key] += value
        
        # Average metrics
        for key in val_losses:
            val_losses[key] /= num_batches
        
        for key in val_metrics:
            val_metrics[key] /= num_batches
        
        # Combine losses and metrics
        val_results = {**val_losses, **val_metrics}
        
        return val_results
    
    def _log_training_step(self, 
                          losses: Dict[str, torch.Tensor],
                          lr_images: torch.Tensor,
                          student_output: torch.Tensor,
                          hr_images: torch.Tensor):
        """Log training step to tensorboard/wandb.
        
        Args:
            losses: Dictionary of losses
            lr_images: Low-resolution input images
            student_output: Student model output
            hr_images: High-resolution target images
        """
        # Log scalar values
        for key, value in losses.items():
            self.writer.add_scalar(f'train/{key}', value.item(), self.global_step)
            
            if self.use_wandb:
                wandb.log({f'train/{key}': value.item()}, step=self.global_step)
        
        # Log learning rate
        current_lr = self.optimizer.param_groups[0]['lr']
        self.writer.add_scalar('train/learning_rate', current_lr, self.global_step)
        
        if self.use_wandb:
            wandb.log({'train/learning_rate': current_lr}, step=self.global_step)
        
        # Log images
        if self.save_images and self.global_step % (self.log_every * 10) == 0:
            self._log_images(lr_images, student_output, hr_images, 'train')
    
    def _log_images(self,
                   lr_images: torch.Tensor,
                   student_output: torch.Tensor,
                   hr_images: torch.Tensor,
                   phase: str):
        """Log sample images.
        
        Args:
            lr_images: Low-resolution input images
            student_output: Student model output
            hr_images: High-resolution target images
            phase: Training phase ('train' or 'val')
        """
        # Take first few images from batch
        num_images = min(self.num_sample_images, lr_images.size(0))
        
        lr_sample = lr_images[:num_images]
        output_sample = student_output[:num_images]
        hr_sample = hr_images[:num_images]
        
        # Clamp values to [0, 1]
        lr_sample = torch.clamp(lr_sample, 0, 1)
        output_sample = torch.clamp(output_sample, 0, 1)
        hr_sample = torch.clamp(hr_sample, 0, 1)
        
        # Log to tensorboard
        self.writer.add_images(f'{phase}/lr_input', lr_sample, self.global_step)
        self.writer.add_images(f'{phase}/student_output', output_sample, self.global_step)
        self.writer.add_images(f'{phase}/hr_target', hr_sample, self.global_step)
        
        # Log to wandb
        if self.use_wandb:
            import numpy as np
            
            # Convert to numpy for wandb
            lr_np = lr_sample.cpu().numpy().transpose(0, 2, 3, 1)
            output_np = output_sample.cpu().numpy().transpose(0, 2, 3, 1)
            hr_np = hr_sample.cpu().numpy().transpose(0, 2, 3, 1)
            
            wandb_images = []
            for i in range(num_images):
                wandb_images.append(wandb.Image(
                    np.concatenate([lr_np[i], output_np[i], hr_np[i]], axis=1),
                    caption=f"LR | Student Output | HR Target"
                ))
            
            wandb.log({f'{phase}/samples': wandb_images}, step=self.global_step)

    def save_checkpoint(self,
                       epoch: int,
                       val_metrics: Dict[str, float],
                       is_best: bool = False):
        """Save model checkpoint.

        Args:
            epoch: Current epoch
            val_metrics: Validation metrics
            is_best: Whether this is the best model so far
        """
        checkpoint_dir = self.config.get('training.checkpoint_dir', 'results/checkpoints')
        os.makedirs(checkpoint_dir, exist_ok=True)

        checkpoint = {
            'epoch': epoch,
            'global_step': self.global_step,
            'student_model_state_dict': self.student_model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'val_metrics': val_metrics,
            'config': self.config,
            'best_ssim': self.best_ssim
        }

        # Save regular checkpoint
        checkpoint_path = os.path.join(checkpoint_dir, f'checkpoint_epoch_{epoch}.pth')
        torch.save(checkpoint, checkpoint_path)

        # Save best model
        if is_best:
            best_path = os.path.join(checkpoint_dir, 'best_model.pth')
            torch.save(checkpoint, best_path)
            print(f"New best model saved with SSIM: {val_metrics['ssim']:.4f}")

        # Save latest model
        latest_path = os.path.join(checkpoint_dir, 'latest_model.pth')
        torch.save(checkpoint, latest_path)

    def load_checkpoint(self, checkpoint_path: str):
        """Load model checkpoint.

        Args:
            checkpoint_path: Path to checkpoint file
        """
        if not os.path.exists(checkpoint_path):
            print(f"Checkpoint not found: {checkpoint_path}")
            return

        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        # Load model state
        self.student_model.load_state_dict(checkpoint['student_model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        if self.scheduler and checkpoint['scheduler_state_dict']:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        # Load training state
        self.current_epoch = checkpoint['epoch']
        self.global_step = checkpoint['global_step']
        self.best_ssim = checkpoint.get('best_ssim', 0.0)

        print(f"Loaded checkpoint from epoch {self.current_epoch}")

    def train(self,
             train_loader: DataLoader,
             val_loader: DataLoader,
             num_epochs: Optional[int] = None):
        """Main training loop.

        Args:
            train_loader: Training data loader
            val_loader: Validation data loader
            num_epochs: Number of epochs to train (overrides config)
        """
        if num_epochs is None:
            num_epochs = self.config.get('training.num_epochs', 100)

        save_every = self.config.get('training.save_every', 10)

        print(f"Starting training for {num_epochs} epochs...")
        print(f"Device: {self.device}")
        print(f"Student model parameters: {sum(p.numel() for p in self.student_model.parameters()):,}")

        for epoch in range(self.current_epoch, num_epochs):
            self.current_epoch = epoch

            # Training
            train_metrics = self.train_epoch(train_loader)

            # Validation
            val_metrics = self.validate(val_loader)

            # Update scheduler
            if self.scheduler:
                if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                    self.scheduler.step(val_metrics['ssim'])
                else:
                    self.scheduler.step()

            # Check if best model
            is_best = val_metrics['ssim'] > self.best_ssim
            if is_best:
                self.best_ssim = val_metrics['ssim']

            # Log epoch results
            self._log_epoch_results(epoch, train_metrics, val_metrics)

            # Save checkpoint
            if (epoch + 1) % save_every == 0 or is_best:
                self.save_checkpoint(epoch, val_metrics, is_best)

            # Early stopping check
            target_ssim = self.config.get('evaluation.target_ssim', 0.90)
            if val_metrics['ssim'] >= target_ssim:
                print(f"Target SSIM {target_ssim:.3f} achieved! Stopping training.")
                break

        print("Training completed!")

        # Save final model
        self.save_checkpoint(self.current_epoch, val_metrics, False)

        # Close logging
        self.writer.close()
        if self.use_wandb:
            wandb.finish()

    def _log_epoch_results(self,
                          epoch: int,
                          train_metrics: Dict[str, float],
                          val_metrics: Dict[str, float]):
        """Log epoch results.

        Args:
            epoch: Current epoch
            train_metrics: Training metrics
            val_metrics: Validation metrics
        """
        # Print results
        print(f"\nEpoch {epoch + 1} Results:")
        print(f"Train Loss: {train_metrics['total_loss']:.4f}")
        print(f"Val Loss: {val_metrics['total_loss']:.4f}")
        print(f"Val SSIM: {val_metrics['ssim']:.4f}")
        print(f"Val PSNR: {val_metrics['psnr']:.2f} dB")
        print(f"Learning Rate: {self.optimizer.param_groups[0]['lr']:.6f}")

        # Log to tensorboard
        for key, value in train_metrics.items():
            self.writer.add_scalar(f'epoch/train_{key}', value, epoch)

        for key, value in val_metrics.items():
            self.writer.add_scalar(f'epoch/val_{key}', value, epoch)

        # Log to wandb
        if self.use_wandb:
            log_dict = {}
            for key, value in train_metrics.items():
                log_dict[f'epoch/train_{key}'] = value
            for key, value in val_metrics.items():
                log_dict[f'epoch/val_{key}'] = value
            log_dict['epoch'] = epoch

            wandb.log(log_dict)
