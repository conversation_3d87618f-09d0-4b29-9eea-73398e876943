"""Comprehensive evaluation framework for image sharpening models."""

import torch
import torch.nn as nn
import numpy as np
import cv2
import os
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import json
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns

from ..utils.metrics import ImageMetrics
from ..inference.optimized_inference import OptimizedInferenceEngine


class ModelEvaluator:
    """Comprehensive model evaluator for image sharpening."""
    
    def __init__(self, 
                 model_path: str,
                 device: str = 'cuda',
                 target_ssim: float = 0.90):
        """Initialize evaluator.
        
        Args:
            model_path: Path to trained model
            device: Device for evaluation
            target_ssim: Target SSIM score
        """
        self.model_path = model_path
        self.device = device
        self.target_ssim = target_ssim
        
        # Initialize inference engine
        self.inference_engine = OptimizedInferenceEngine(
            model_path=model_path,
            device=device,
            use_half_precision=(device == 'cuda')
        )
        
        # Initialize metrics
        self.metrics = ImageMetrics(device)
        
        print(f"Initialized evaluator with model: {model_path}")
        print(f"Target SSIM: {target_ssim}")
    
    def evaluate_on_dataset(self, 
                           dataset_dir: str,
                           categories: List[str] = None,
                           max_images_per_category: int = None) -> Dict:
        """Evaluate model on benchmark dataset.
        
        Args:
            dataset_dir: Directory containing benchmark images
            categories: List of categories to evaluate
            max_images_per_category: Maximum images per category
            
        Returns:
            Evaluation results
        """
        if categories is None:
            categories = ["text", "nature", "people", "animals", "games"]
        
        print(f"Evaluating on benchmark dataset: {dataset_dir}")
        print(f"Categories: {categories}")
        
        results = {
            'overall': {'ssim': [], 'psnr': [], 'lpips': [], 'inference_times': []},
            'by_category': {},
            'summary': {}
        }
        
        total_images = 0
        
        for category in categories:
            category_dir = os.path.join(dataset_dir, category)
            
            if not os.path.exists(category_dir):
                print(f"Warning: Category directory not found: {category_dir}")
                continue
            
            # Get image files
            image_files = []
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                image_files.extend([
                    os.path.join(category_dir, f)
                    for f in os.listdir(category_dir)
                    if f.lower().endswith(ext)
                ])
            
            if max_images_per_category:
                image_files = image_files[:max_images_per_category]
            
            if not image_files:
                print(f"Warning: No images found in category: {category}")
                continue
            
            print(f"Evaluating {len(image_files)} images in category: {category}")
            
            category_results = {
                'ssim': [], 'psnr': [], 'lpips': [], 'inference_times': [],
                'image_paths': []
            }
            
            for image_path in tqdm(image_files, desc=f"Processing {category}"):
                try:
                    # Load and process image
                    original_image = cv2.imread(image_path)
                    if original_image is None:
                        continue
                    
                    # Apply degradation to create input
                    degraded_image = self._apply_test_degradation(original_image)
                    
                    # Run inference
                    enhanced_image, inference_time = self.inference_engine.infer_image(degraded_image)
                    
                    # Calculate metrics
                    metrics = self._calculate_metrics(enhanced_image, original_image)
                    
                    # Store results
                    category_results['ssim'].append(metrics['ssim'])
                    category_results['psnr'].append(metrics['psnr'])
                    category_results['lpips'].append(metrics['lpips'])
                    category_results['inference_times'].append(inference_time)
                    category_results['image_paths'].append(image_path)
                    
                    # Add to overall results
                    results['overall']['ssim'].append(metrics['ssim'])
                    results['overall']['psnr'].append(metrics['psnr'])
                    results['overall']['lpips'].append(metrics['lpips'])
                    results['overall']['inference_times'].append(inference_time)
                    
                    total_images += 1
                    
                except Exception as e:
                    print(f"Error processing {image_path}: {e}")
                    continue
            
            # Calculate category statistics
            if category_results['ssim']:
                results['by_category'][category] = {
                    'count': len(category_results['ssim']),
                    'ssim_mean': np.mean(category_results['ssim']),
                    'ssim_std': np.std(category_results['ssim']),
                    'psnr_mean': np.mean(category_results['psnr']),
                    'psnr_std': np.std(category_results['psnr']),
                    'lpips_mean': np.mean(category_results['lpips']),
                    'lpips_std': np.std(category_results['lpips']),
                    'avg_inference_time': np.mean(category_results['inference_times']),
                    'raw_data': category_results
                }
        
        # Calculate overall statistics
        if results['overall']['ssim']:
            results['summary'] = {
                'total_images': total_images,
                'overall_ssim_mean': np.mean(results['overall']['ssim']),
                'overall_ssim_std': np.std(results['overall']['ssim']),
                'overall_psnr_mean': np.mean(results['overall']['psnr']),
                'overall_psnr_std': np.std(results['overall']['psnr']),
                'overall_lpips_mean': np.mean(results['overall']['lpips']),
                'overall_lpips_std': np.std(results['overall']['lpips']),
                'avg_inference_time': np.mean(results['overall']['inference_times']),
                'avg_fps': 1.0 / np.mean(results['overall']['inference_times']),
                'target_ssim_achieved': np.mean(results['overall']['ssim']) >= self.target_ssim,
                'images_above_target': sum(1 for s in results['overall']['ssim'] if s >= self.target_ssim),
                'percentage_above_target': (sum(1 for s in results['overall']['ssim'] if s >= self.target_ssim) / len(results['overall']['ssim'])) * 100
            }
        
        return results
    
    def _apply_test_degradation(self, image: np.ndarray) -> np.ndarray:
        """Apply test degradation to simulate video conferencing conditions.
        
        Args:
            image: Original image
            
        Returns:
            Degraded image
        """
        # Simulate typical video conferencing degradations
        h, w = image.shape[:2]
        
        # Downscale and upscale
        scale_factor = np.random.choice([2, 3, 4])
        small_h, small_w = h // scale_factor, w // scale_factor
        
        # Downscale
        degraded = cv2.resize(image, (small_w, small_h), interpolation=cv2.INTER_CUBIC)
        
        # Add noise
        noise_level = np.random.choice([5, 10, 15])
        noise = np.random.normal(0, noise_level, degraded.shape)
        degraded = np.clip(degraded + noise, 0, 255).astype(np.uint8)
        
        # JPEG compression
        quality = np.random.choice([70, 80, 90])
        encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), quality]
        _, encimg = cv2.imencode('.jpg', degraded, encode_param)
        degraded = cv2.imdecode(encimg, 1)
        
        # Upscale back to original size
        degraded = cv2.resize(degraded, (w, h), interpolation=cv2.INTER_CUBIC)
        
        return degraded
    
    def _calculate_metrics(self, enhanced: np.ndarray, reference: np.ndarray) -> Dict[str, float]:
        """Calculate image quality metrics.
        
        Args:
            enhanced: Enhanced image
            reference: Reference image
            
        Returns:
            Dictionary of metrics
        """
        # Convert to tensors
        enhanced_tensor = torch.from_numpy(enhanced.transpose(2, 0, 1)).unsqueeze(0).float() / 255.0
        reference_tensor = torch.from_numpy(reference.transpose(2, 0, 1)).unsqueeze(0).float() / 255.0
        
        enhanced_tensor = enhanced_tensor.to(self.device)
        reference_tensor = reference_tensor.to(self.device)
        
        # Calculate metrics
        metrics = self.metrics.calculate_all_metrics(enhanced_tensor, reference_tensor)
        
        return metrics
    
    def generate_evaluation_report(self, 
                                 results: Dict,
                                 output_dir: str,
                                 save_visualizations: bool = True) -> str:
        """Generate comprehensive evaluation report.
        
        Args:
            results: Evaluation results
            output_dir: Output directory
            save_visualizations: Whether to save visualization plots
            
        Returns:
            Path to generated report
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate text report
        report_path = os.path.join(output_dir, 'evaluation_report.txt')
        
        with open(report_path, 'w') as f:
            f.write("Image Sharpening Model - Evaluation Report\n")
            f.write("=" * 50 + "\n\n")
            
            # Model information
            f.write(f"Model: {self.model_path}\n")
            f.write(f"Device: {self.device}\n")
            f.write(f"Target SSIM: {self.target_ssim}\n\n")
            
            # Overall results
            summary = results['summary']
            f.write("Overall Results:\n")
            f.write("-" * 20 + "\n")
            f.write(f"Total images evaluated: {summary['total_images']}\n")
            f.write(f"Average SSIM: {summary['overall_ssim_mean']:.4f} ± {summary['overall_ssim_std']:.4f}\n")
            f.write(f"Average PSNR: {summary['overall_psnr_mean']:.2f} ± {summary['overall_psnr_std']:.2f} dB\n")
            f.write(f"Average LPIPS: {summary['overall_lpips_mean']:.4f} ± {summary['overall_lpips_std']:.4f}\n")
            f.write(f"Average inference time: {summary['avg_inference_time']*1000:.2f} ms\n")
            f.write(f"Average FPS: {summary['avg_fps']:.1f}\n\n")
            
            # Target achievement
            f.write("Target Achievement:\n")
            f.write("-" * 20 + "\n")
            if summary['target_ssim_achieved']:
                f.write(f"✓ Target SSIM {self.target_ssim} achieved!\n")
            else:
                f.write(f"✗ Target SSIM {self.target_ssim} not achieved\n")
            
            f.write(f"Images above target: {summary['images_above_target']}/{summary['total_images']} ")
            f.write(f"({summary['percentage_above_target']:.1f}%)\n\n")
            
            # Category breakdown
            f.write("Results by Category:\n")
            f.write("-" * 20 + "\n")
            
            for category, cat_results in results['by_category'].items():
                f.write(f"\n{category.upper()}:\n")
                f.write(f"  Images: {cat_results['count']}\n")
                f.write(f"  SSIM: {cat_results['ssim_mean']:.4f} ± {cat_results['ssim_std']:.4f}\n")
                f.write(f"  PSNR: {cat_results['psnr_mean']:.2f} ± {cat_results['psnr_std']:.2f} dB\n")
                f.write(f"  LPIPS: {cat_results['lpips_mean']:.4f} ± {cat_results['lpips_std']:.4f}\n")
                f.write(f"  Avg time: {cat_results['avg_inference_time']*1000:.2f} ms\n")
            
            # Performance analysis
            f.write(f"\nPerformance Analysis:\n")
            f.write("-" * 20 + "\n")
            
            if summary['avg_fps'] >= 30:
                f.write("✓ Real-time performance achieved (≥30 FPS)\n")
            else:
                f.write("✗ Real-time performance not achieved (<30 FPS)\n")
            
            if summary['avg_fps'] >= 60:
                f.write("✓ High-performance target achieved (≥60 FPS)\n")
            else:
                f.write("✗ High-performance target not achieved (<60 FPS)\n")
            
            # Recommendations
            f.write(f"\nRecommendations:\n")
            f.write("-" * 20 + "\n")
            
            if not summary['target_ssim_achieved']:
                gap = self.target_ssim - summary['overall_ssim_mean']
                if gap > 0.05:
                    f.write("- Consider using a larger student model\n")
                    f.write("- Increase training epochs\n")
                    f.write("- Adjust distillation parameters\n")
                else:
                    f.write("- Fine-tune hyperparameters\n")
                    f.write("- Consider ensemble methods\n")
            
            if summary['avg_fps'] < 30:
                f.write("- Optimize model for faster inference\n")
                f.write("- Consider model quantization\n")
                f.write("- Use TensorRT or ONNX optimization\n")
        
        # Save detailed results as JSON
        json_path = os.path.join(output_dir, 'evaluation_results.json')
        
        # Prepare JSON-serializable results
        json_results = {
            'model_path': self.model_path,
            'target_ssim': self.target_ssim,
            'summary': summary,
            'by_category': {}
        }
        
        for category, cat_results in results['by_category'].items():
            json_results['by_category'][category] = {
                k: v for k, v in cat_results.items() if k != 'raw_data'
            }
        
        with open(json_path, 'w') as f:
            json.dump(json_results, f, indent=2)
        
        # Generate visualizations
        if save_visualizations:
            self._generate_visualizations(results, output_dir)
        
        print(f"Evaluation report saved to: {report_path}")
        print(f"Detailed results saved to: {json_path}")
        
        return report_path
    
    def _generate_visualizations(self, results: Dict, output_dir: str):
        """Generate visualization plots.
        
        Args:
            results: Evaluation results
            output_dir: Output directory
        """
        # SSIM distribution by category
        plt.figure(figsize=(12, 8))
        
        categories = list(results['by_category'].keys())
        ssim_data = [results['by_category'][cat]['raw_data']['ssim'] for cat in categories]
        
        plt.boxplot(ssim_data, labels=categories)
        plt.axhline(y=self.target_ssim, color='r', linestyle='--', label=f'Target SSIM ({self.target_ssim})')
        plt.ylabel('SSIM Score')
        plt.title('SSIM Distribution by Category')
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'ssim_by_category.png'), dpi=300)
        plt.close()
        
        # Overall metrics comparison
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # SSIM histogram
        axes[0].hist(results['overall']['ssim'], bins=30, alpha=0.7, color='blue')
        axes[0].axvline(x=self.target_ssim, color='r', linestyle='--', label=f'Target ({self.target_ssim})')
        axes[0].set_xlabel('SSIM Score')
        axes[0].set_ylabel('Frequency')
        axes[0].set_title('SSIM Distribution')
        axes[0].legend()
        
        # PSNR histogram
        axes[1].hist(results['overall']['psnr'], bins=30, alpha=0.7, color='green')
        axes[1].set_xlabel('PSNR (dB)')
        axes[1].set_ylabel('Frequency')
        axes[1].set_title('PSNR Distribution')
        
        # Inference time histogram
        inference_times_ms = [t * 1000 for t in results['overall']['inference_times']]
        axes[2].hist(inference_times_ms, bins=30, alpha=0.7, color='orange')
        axes[2].set_xlabel('Inference Time (ms)')
        axes[2].set_ylabel('Frequency')
        axes[2].set_title('Inference Time Distribution')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'metrics_distribution.png'), dpi=300)
        plt.close()
        
        # Category performance comparison
        plt.figure(figsize=(10, 6))
        
        categories = list(results['by_category'].keys())
        ssim_means = [results['by_category'][cat]['ssim_mean'] for cat in categories]
        ssim_stds = [results['by_category'][cat]['ssim_std'] for cat in categories]
        
        x_pos = np.arange(len(categories))
        plt.bar(x_pos, ssim_means, yerr=ssim_stds, capsize=5, alpha=0.7)
        plt.axhline(y=self.target_ssim, color='r', linestyle='--', label=f'Target SSIM ({self.target_ssim})')
        plt.xlabel('Category')
        plt.ylabel('Average SSIM Score')
        plt.title('Average SSIM by Category')
        plt.xticks(x_pos, categories, rotation=45)
        plt.legend()
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'ssim_by_category_bar.png'), dpi=300)
        plt.close()
        
        print(f"Visualizations saved to: {output_dir}")


def create_benchmark_dataset(output_dir: str, 
                           categories: List[str] = None,
                           images_per_category: int = 25) -> str:
    """Create a comprehensive benchmark dataset.
    
    Args:
        output_dir: Output directory for benchmark dataset
        categories: List of categories to create
        images_per_category: Number of images per category
        
    Returns:
        Path to created benchmark dataset
    """
    if categories is None:
        categories = ["text", "nature", "people", "animals", "games"]
    
    print(f"Creating benchmark dataset with {len(categories)} categories")
    print(f"Images per category: {images_per_category}")
    
    for category in categories:
        category_dir = os.path.join(output_dir, category)
        os.makedirs(category_dir, exist_ok=True)
        
        print(f"Creating {category} images...")
        
        for i in tqdm(range(images_per_category), desc=f"Creating {category}"):
            image = _create_category_image(category, i)
            
            filename = f"{category}_{i+1:03d}.png"
            image_path = os.path.join(category_dir, filename)
            cv2.imwrite(image_path, image)
    
    print(f"Benchmark dataset created at: {output_dir}")
    return output_dir


def _create_category_image(category: str, index: int) -> np.ndarray:
    """Create a synthetic image for a specific category.
    
    Args:
        category: Image category
        index: Image index
        
    Returns:
        Generated image
    """
    height, width = 512, 512
    
    if category == "text":
        # Text-heavy image
        image = np.ones((height, width, 3), dtype=np.uint8) * 255
        
        # Add multiple lines of text with different fonts and sizes
        texts = [
            "Sample Text Document",
            "This is a test image for text category",
            "Image sharpening evaluation",
            f"Document #{index + 1}",
            "Lorem ipsum dolor sit amet",
            "consectetur adipiscing elit",
            "sed do eiusmod tempor incididunt",
            "ut labore et dolore magna aliqua"
        ]
        
        for i, text in enumerate(texts):
            y_pos = 50 + i * 50
            font_scale = 0.6 + (i % 3) * 0.2
            cv2.putText(image, text, (20, y_pos), 
                       cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), 2)
        
        # Add some geometric elements
        cv2.rectangle(image, (400, 50), (480, 130), (200, 200, 200), 2)
        cv2.circle(image, (440, 200), 30, (150, 150, 150), 2)
        
    elif category == "nature":
        # Nature-like patterns
        image = np.random.randint(50, 150, (height, width, 3), dtype=np.uint8)
        
        # Add tree-like structures
        for _ in range(8):
            x = np.random.randint(50, width - 50)
            y = np.random.randint(height//2, height - 50)
            tree_height = np.random.randint(80, 150)
            cv2.line(image, (x, y), (x, y - tree_height), (34, 139, 34), 8)
            
            # Add branches
            for _ in range(3):
                branch_x = x + np.random.randint(-30, 30)
                branch_y = y - np.random.randint(20, tree_height - 20)
                cv2.line(image, (x, branch_y), (branch_x, branch_y - 20), (34, 139, 34), 4)
        
        # Add foliage
        for _ in range(50):
            x = np.random.randint(0, width)
            y = np.random.randint(0, height//2 + 100)
            radius = np.random.randint(5, 20)
            color = (0, np.random.randint(100, 255), 0)
            cv2.circle(image, (x, y), radius, color, -1)
        
        # Add sky gradient
        for i in range(height//3):
            color_intensity = int(200 + 55 * (1 - i / (height//3)))
            image[i, :] = [color_intensity, color_intensity, 255]
    
    elif category == "people":
        # Simple face-like structures
        image = np.random.randint(200, 240, (height, width, 3), dtype=np.uint8)
        
        # Multiple faces
        for face_idx in range(2):
            center_x = width//4 + face_idx * width//2
            center_y = height//3 + face_idx * height//4
            
            # Face outline
            cv2.circle(image, (center_x, center_y), 80, (255, 220, 177), -1)
            cv2.circle(image, (center_x, center_y), 80, (200, 180, 150), 3)
            
            # Eyes
            cv2.circle(image, (center_x - 25, center_y - 15), 8, (0, 0, 0), -1)
            cv2.circle(image, (center_x + 25, center_y - 15), 8, (0, 0, 0), -1)
            cv2.circle(image, (center_x - 25, center_y - 15), 3, (255, 255, 255), -1)
            cv2.circle(image, (center_x + 25, center_y - 15), 3, (255, 255, 255), -1)
            
            # Nose
            cv2.line(image, (center_x, center_y - 5), (center_x - 5, center_y + 10), (180, 150, 120), 2)
            
            # Mouth
            cv2.ellipse(image, (center_x, center_y + 25), (15, 8), 0, 0, 180, (200, 100, 100), 2)
            
            # Hair
            cv2.ellipse(image, (center_x, center_y - 60), (60, 40), 0, 0, 180, (101, 67, 33), -1)
    
    elif category == "animals":
        # Animal-like shapes
        image = np.random.randint(100, 180, (height, width, 3), dtype=np.uint8)
        
        # Multiple animals
        for animal_idx in range(2):
            center_x = width//4 + animal_idx * width//2
            center_y = height//2 + animal_idx * 50
            
            # Body
            cv2.ellipse(image, (center_x, center_y), (60, 40), 0, 0, 360, (139, 69, 19), -1)
            
            # Head
            cv2.circle(image, (center_x, center_y - 50), 35, (139, 69, 19), -1)
            
            # Ears
            cv2.circle(image, (center_x - 20, center_y - 70), 12, (139, 69, 19), -1)
            cv2.circle(image, (center_x + 20, center_y - 70), 12, (139, 69, 19), -1)
            
            # Eyes
            cv2.circle(image, (center_x - 12, center_y - 55), 4, (0, 0, 0), -1)
            cv2.circle(image, (center_x + 12, center_y - 55), 4, (0, 0, 0), -1)
            
            # Nose
            cv2.circle(image, (center_x, center_y - 45), 3, (0, 0, 0), -1)
            
            # Legs
            for leg_x in [center_x - 30, center_x - 10, center_x + 10, center_x + 30]:
                cv2.rectangle(image, (leg_x - 5, center_y + 20), (leg_x + 5, center_y + 50), (139, 69, 19), -1)
    
    elif category == "games":
        # Game-like graphics with patterns
        image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Checkerboard background
        square_size = 32
        for row in range(0, height, square_size):
            for col in range(0, width, square_size):
                if (row//square_size + col//square_size) % 2 == 0:
                    color = (50, 50, 150)
                else:
                    color = (150, 50, 50)
                cv2.rectangle(image, (col, row), (col + square_size, row + square_size), color, -1)
        
        # Game elements
        # Player character
        cv2.circle(image, (width//4, height//4), 25, (255, 255, 0), -1)
        cv2.circle(image, (width//4, height//4), 25, (200, 200, 0), 3)
        
        # Enemies
        for i in range(3):
            x = width//2 + i * 80
            y = height//3 + i * 40
            cv2.rectangle(image, (x - 15, y - 15), (x + 15, y + 15), (255, 0, 0), -1)
            cv2.rectangle(image, (x - 15, y - 15), (x + 15, y + 15), (200, 0, 0), 2)
        
        # Power-ups
        for i in range(4):
            x = 100 + i * 100
            y = 3 * height // 4
            cv2.circle(image, (x, y), 12, (0, 255, 0), -1)
            cv2.circle(image, (x, y), 12, (0, 200, 0), 2)
        
        # Score display
        cv2.putText(image, f"SCORE: {(index + 1) * 1000}", (20, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    # Add some noise for realism
    noise = np.random.normal(0, 10, image.shape)
    image = np.clip(image + noise, 0, 255).astype(np.uint8)
    
    return image
