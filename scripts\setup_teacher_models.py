"""Script to download and setup pretrained teacher models."""

import os
import argparse
import requests
from pathlib import Path
import torch
from tqdm import tqdm
import sys

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.models.teacher_models import TeacherModelManager


def download_file(url: str, destination: str, description: str = "Downloading"):
    """Download a file with progress bar.
    
    Args:
        url: URL to download from
        destination: Local file path to save to
        description: Description for progress bar
    """
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        
        with open(destination, 'wb') as f, tqdm(
            desc=description,
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as pbar:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    pbar.update(len(chunk))
        
        print(f"✓ Downloaded {description} to {destination}")
        return True
        
    except Exception as e:
        print(f"✗ Failed to download {description}: {e}")
        return False


def create_dummy_teacher_model(model_name: str, save_path: str, device: str = 'cpu'):
    """Create a dummy teacher model for testing purposes.
    
    Args:
        model_name: Name of the model to create
        save_path: Path to save the model
        device: Device to create model on
    """
    try:
        print(f"Creating dummy {model_name} model...")
        
        # Create teacher model manager
        teacher_manager = TeacherModelManager(model_name, device)
        
        # Create model with smaller parameters for faster testing
        if model_name.lower() == 'edsr':
            model = teacher_manager.create_model(
                num_features=64,
                num_blocks=8,
                upscale_factor=1
            )
        elif model_name.lower() == 'esrgan':
            model = teacher_manager.create_model(
                num_features=32,
                num_blocks=8,
                upscale_factor=1
            )
        elif model_name.lower() == 'srresnet':
            model = teacher_manager.create_model(
                num_features=32,
                num_blocks=8,
                upscale_factor=1
            )
        else:
            raise ValueError(f"Unknown model: {model_name}")
        
        # Save model
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        torch.save({
            'model': model.state_dict(),
            'model_name': model_name,
            'note': 'This is a dummy model for testing purposes'
        }, save_path)
        
        print(f"✓ Created dummy {model_name} model at {save_path}")
        return True
        
    except Exception as e:
        print(f"✗ Failed to create dummy {model_name} model: {e}")
        return False


def test_teacher_model(model_name: str, model_path: str, device: str = 'cpu'):
    """Test loading and inference of teacher model.
    
    Args:
        model_name: Name of the model
        model_path: Path to model checkpoint
        device: Device to test on
    """
    try:
        print(f"Testing {model_name} model...")
        
        # Create teacher model manager
        teacher_manager = TeacherModelManager(model_name, device)
        
        # Load model
        model = teacher_manager.load_pretrained(model_path)
        
        # Test inference
        batch_size = 2
        channels = 3
        height, width = 256, 256
        
        dummy_input = torch.randn(batch_size, channels, height, width).to(device)
        
        # Test forward pass
        with torch.no_grad():
            output = model(dummy_input)
        
        print(f"✓ Input shape: {dummy_input.shape}")
        print(f"✓ Output shape: {output.shape}")
        
        # Test teacher manager methods
        teacher_output = teacher_manager.get_teacher_outputs(dummy_input)
        print(f"✓ Teacher output shape: {teacher_output.shape}")
        
        # Test intermediate features
        features = teacher_manager.get_intermediate_features(dummy_input)
        print(f"✓ Intermediate features: {list(features.keys())}")
        
        print(f"✓ {model_name} model test passed")
        return True
        
    except Exception as e:
        print(f"✗ {model_name} model test failed: {e}")
        return False


def main():
    """Main function to setup teacher models."""
    parser = argparse.ArgumentParser(description='Setup pretrained teacher models')
    parser.add_argument('--models-dir', type=str, default='models/pretrained',
                       help='Directory to save pretrained models')
    parser.add_argument('--create-dummy', action='store_true',
                       help='Create dummy models for testing')
    parser.add_argument('--test-models', action='store_true',
                       help='Test model loading and inference')
    parser.add_argument('--device', type=str, default='cpu',
                       choices=['cpu', 'cuda'], help='Device to use')
    
    args = parser.parse_args()
    
    # Create models directory
    models_dir = Path(args.models_dir)
    models_dir.mkdir(parents=True, exist_ok=True)
    
    print("Teacher Models Setup")
    print("=" * 50)
    
    # Available teacher models
    teacher_models = ['edsr', 'esrgan', 'srresnet']
    
    # URLs for pretrained models (these are placeholders - replace with actual URLs)
    pretrained_urls = {
        'edsr': None,  # Add actual URL when available
        'esrgan': None,  # Add actual URL when available
        'srresnet': None,  # Add actual URL when available
    }
    
    success_count = 0
    
    for model_name in teacher_models:
        print(f"\nSetting up {model_name.upper()} model...")
        print("-" * 30)
        
        model_path = models_dir / f"{model_name}_teacher.pth"
        
        # Try to download pretrained model
        if pretrained_urls[model_name] and not model_path.exists():
            print(f"Downloading pretrained {model_name} model...")
            if download_file(pretrained_urls[model_name], str(model_path), 
                           f"{model_name} model"):
                print(f"✓ Downloaded {model_name} model")
            else:
                print(f"✗ Failed to download {model_name} model")
        
        # Create dummy model if requested or if download failed
        if args.create_dummy or not model_path.exists():
            if create_dummy_teacher_model(model_name, str(model_path), args.device):
                success_count += 1
        
        # Test model if requested
        if args.test_models and model_path.exists():
            if test_teacher_model(model_name, str(model_path), args.device):
                print(f"✓ {model_name} model is working correctly")
            else:
                print(f"✗ {model_name} model test failed")
    
    print("\n" + "=" * 50)
    print("Setup Summary")
    print("=" * 50)
    
    print(f"Models directory: {models_dir}")
    print(f"Successfully set up: {success_count}/{len(teacher_models)} models")
    
    # List available models
    print("\nAvailable teacher models:")
    for model_name in teacher_models:
        model_path = models_dir / f"{model_name}_teacher.pth"
        status = "✓" if model_path.exists() else "✗"
        print(f"  {status} {model_name.upper()}: {model_path}")
    
    print("\nNext steps:")
    print("1. Update config.yaml with the teacher model you want to use")
    print("2. Set the correct path in config.yaml: model.teacher.pretrained_path")
    print("3. For real pretrained models, download from official sources:")
    print("   - EDSR: https://github.com/sanghyun-son/EDSR-PyTorch")
    print("   - ESRGAN: https://github.com/xinntao/ESRGAN")
    print("   - SRResNet: https://github.com/leftthomas/SRGAN")


if __name__ == '__main__':
    main()
