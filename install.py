"""Installation script for the image sharpening project."""

import subprocess
import sys
import os


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed:")
        print(f"Error: {e.stderr}")
        return False


def main():
    """Main installation function."""
    print("Image Sharpening Knowledge Distillation - Installation Script")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"Python version: {sys.version}")
    
    # Check if pip is available
    try:
        import pip
        print(f"pip version: {pip.__version__}")
    except ImportError:
        print("Error: pip is not available")
        sys.exit(1)
    
    # Install requirements
    if not run_command("pip install -r requirements.txt", "Installing requirements"):
        print("Failed to install requirements. Please check the error messages above.")
        sys.exit(1)
    
    # Install the package in development mode
    if not run_command("pip install -e .", "Installing package in development mode"):
        print("Failed to install package. Please check the error messages above.")
        sys.exit(1)
    
    # Verify installation
    print("\nVerifying installation...")
    try:
        import torch
        print(f"✓ PyTorch version: {torch.__version__}")
        print(f"✓ CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✓ CUDA version: {torch.version.cuda}")
            print(f"✓ GPU count: {torch.cuda.device_count()}")
    except ImportError:
        print("✗ PyTorch installation verification failed")
    
    try:
        import cv2
        print(f"✓ OpenCV version: {cv2.__version__}")
    except ImportError:
        print("✗ OpenCV installation verification failed")
    
    try:
        from PIL import Image
        print("✓ PIL/Pillow is available")
    except ImportError:
        print("✗ PIL/Pillow installation verification failed")
    
    print("\n" + "=" * 60)
    print("Installation completed!")
    print("\nNext steps:")
    print("1. Prepare your dataset in the data/ directory")
    print("2. Modify config.yaml according to your needs")
    print("3. Run: python main.py --mode train")
    print("\nFor more information, see README.md")


if __name__ == "__main__":
    main()
