"""Main evaluation script for trained models."""

import os
import argparse
import sys
from pathlib import Path

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.evaluation.evaluator import ModelEvaluator, create_benchmark_dataset
from src.utils.config import load_config


def main():
    """Main evaluation function."""
    parser = argparse.ArgumentParser(description='Evaluate trained image sharpening model')
    parser.add_argument('--model-path', type=str, required=True,
                       help='Path to trained model')
    parser.add_argument('--config', type=str, default='config.yaml',
                       help='Path to configuration file')
    parser.add_argument('--benchmark-dir', type=str, default='data/benchmark',
                       help='Path to benchmark dataset')
    parser.add_argument('--output-dir', type=str, default='results/evaluation',
                       help='Output directory for evaluation results')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cpu', 'cuda'], help='Device to use')
    parser.add_argument('--create-benchmark', action='store_true',
                       help='Create benchmark dataset if it does not exist')
    parser.add_argument('--max-images', type=int, default=None,
                       help='Maximum images per category to evaluate')
    parser.add_argument('--categories', nargs='+', 
                       default=['text', 'nature', 'people', 'animals', 'games'],
                       help='Categories to evaluate')
    parser.add_argument('--target-ssim', type=float, default=0.90,
                       help='Target SSIM score')
    
    args = parser.parse_args()
    
    # Load configuration
    try:
        config = load_config(args.config)
    except Exception as e:
        print(f"Warning: Could not load config: {e}")
        config = None
    
    # Set device
    device = args.device
    if device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        device = 'cpu'
    
    print("Model Evaluation")
    print("=" * 50)
    print(f"Model: {args.model_path}")
    print(f"Benchmark dataset: {args.benchmark_dir}")
    print(f"Device: {device}")
    print(f"Target SSIM: {args.target_ssim}")
    print(f"Categories: {args.categories}")
    
    # Check if model exists
    if not os.path.exists(args.model_path):
        print(f"Error: Model not found at {args.model_path}")
        return
    
    # Check benchmark dataset
    benchmark_exists = os.path.exists(args.benchmark_dir)
    
    if not benchmark_exists:
        if args.create_benchmark:
            print(f"\nCreating benchmark dataset at {args.benchmark_dir}...")
            create_benchmark_dataset(
                output_dir=args.benchmark_dir,
                categories=args.categories,
                images_per_category=25
            )
        else:
            print(f"Error: Benchmark dataset not found at {args.benchmark_dir}")
            print("Use --create-benchmark to create a synthetic benchmark dataset")
            return
    else:
        # Check if all categories exist
        missing_categories = []
        for category in args.categories:
            category_dir = os.path.join(args.benchmark_dir, category)
            if not os.path.exists(category_dir):
                missing_categories.append(category)
        
        if missing_categories:
            print(f"Warning: Missing categories: {missing_categories}")
            if args.create_benchmark:
                print("Creating missing categories...")
                for category in missing_categories:
                    category_dir = os.path.join(args.benchmark_dir, category)
                    os.makedirs(category_dir, exist_ok=True)
                    
                    # Create images for missing category
                    from src.evaluation.evaluator import _create_category_image
                    import cv2
                    from tqdm import tqdm
                    
                    for i in tqdm(range(25), desc=f"Creating {category}"):
                        image = _create_category_image(category, i)
                        filename = f"{category}_{i+1:03d}.png"
                        image_path = os.path.join(category_dir, filename)
                        cv2.imwrite(image_path, image)
            else:
                args.categories = [cat for cat in args.categories if cat not in missing_categories]
                print(f"Proceeding with available categories: {args.categories}")
    
    # Initialize evaluator
    print(f"\nInitializing evaluator...")
    try:
        evaluator = ModelEvaluator(
            model_path=args.model_path,
            device=device,
            target_ssim=args.target_ssim
        )
    except Exception as e:
        print(f"Error initializing evaluator: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Run evaluation
    print(f"\nRunning evaluation on benchmark dataset...")
    try:
        results = evaluator.evaluate_on_dataset(
            dataset_dir=args.benchmark_dir,
            categories=args.categories,
            max_images_per_category=args.max_images
        )
        
        if not results['overall']['ssim']:
            print("Error: No images were successfully processed")
            return
        
        # Generate report
        print(f"\nGenerating evaluation report...")
        report_path = evaluator.generate_evaluation_report(
            results=results,
            output_dir=args.output_dir,
            save_visualizations=True
        )
        
        # Print summary
        summary = results['summary']
        print(f"\n" + "=" * 50)
        print("EVALUATION SUMMARY")
        print("=" * 50)
        print(f"Total images evaluated: {summary['total_images']}")
        print(f"Average SSIM: {summary['overall_ssim_mean']:.4f} ± {summary['overall_ssim_std']:.4f}")
        print(f"Average PSNR: {summary['overall_psnr_mean']:.2f} ± {summary['overall_psnr_std']:.2f} dB")
        print(f"Average LPIPS: {summary['overall_lpips_mean']:.4f} ± {summary['overall_lpips_std']:.4f}")
        print(f"Average inference time: {summary['avg_inference_time']*1000:.2f} ms")
        print(f"Average FPS: {summary['avg_fps']:.1f}")
        
        print(f"\nTarget Achievement:")
        if summary['target_ssim_achieved']:
            print(f"✓ Target SSIM {args.target_ssim} achieved!")
        else:
            print(f"✗ Target SSIM {args.target_ssim} not achieved")
            gap = args.target_ssim - summary['overall_ssim_mean']
            print(f"  Gap: {gap:.4f}")
        
        print(f"Images above target: {summary['images_above_target']}/{summary['total_images']} ")
        print(f"({summary['percentage_above_target']:.1f}%)")
        
        print(f"\nPerformance:")
        if summary['avg_fps'] >= 60:
            print("✓ High-performance target achieved (≥60 FPS)")
        elif summary['avg_fps'] >= 30:
            print("✓ Real-time target achieved (≥30 FPS)")
        else:
            print("✗ Real-time target not achieved (<30 FPS)")
        
        # Category breakdown
        print(f"\nResults by Category:")
        print("-" * 30)
        for category, cat_results in results['by_category'].items():
            ssim_mean = cat_results['ssim_mean']
            status = "✓" if ssim_mean >= args.target_ssim else "✗"
            print(f"{status} {category.upper()}: SSIM {ssim_mean:.4f} ({cat_results['count']} images)")
        
        # Recommendations
        print(f"\nRecommendations:")
        print("-" * 20)
        
        if not summary['target_ssim_achieved']:
            gap = args.target_ssim - summary['overall_ssim_mean']
            if gap > 0.05:
                print("- Consider using a larger student model")
                print("- Increase training epochs")
                print("- Adjust distillation parameters (alpha/beta)")
                print("- Use a better teacher model")
            else:
                print("- Fine-tune hyperparameters")
                print("- Increase training data diversity")
                print("- Consider ensemble methods")
        
        if summary['avg_fps'] < 30:
            print("- Optimize model for faster inference:")
            print("  * Apply quantization (FP16/INT8)")
            print("  * Convert to ONNX/TensorRT")
            print("  * Use model pruning")
            print("  * Reduce model complexity")
        elif summary['avg_fps'] < 60:
            print("- For higher performance:")
            print("  * Apply advanced optimizations")
            print("  * Consider hardware-specific optimizations")
        
        # Best and worst performing categories
        if len(results['by_category']) > 1:
            best_category = max(results['by_category'].items(), key=lambda x: x[1]['ssim_mean'])
            worst_category = min(results['by_category'].items(), key=lambda x: x[1]['ssim_mean'])
            
            print(f"\nCategory Analysis:")
            print(f"Best performing: {best_category[0]} (SSIM: {best_category[1]['ssim_mean']:.4f})")
            print(f"Worst performing: {worst_category[0]} (SSIM: {worst_category[1]['ssim_mean']:.4f})")
            
            if worst_category[1]['ssim_mean'] < args.target_ssim:
                print(f"Consider improving performance on {worst_category[0]} category")
        
        print(f"\nDetailed results saved to: {args.output_dir}")
        print("Files generated:")
        print(f"  - evaluation_report.txt: Comprehensive text report")
        print(f"  - evaluation_results.json: Detailed numerical results")
        print(f"  - *.png: Visualization plots")
        
        print(f"\nEvaluation completed successfully!")
        
    except Exception as e:
        print(f"Error during evaluation: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    import torch
    main()
