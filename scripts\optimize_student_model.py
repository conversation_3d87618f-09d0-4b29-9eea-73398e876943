"""Script to optimize student models for deployment."""

import os
import argparse
import torch
import torch.nn as nn
import sys
from pathlib import Path

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.models.student_models import StudentModelFactory, create_optimized_student_models
from src.utils.config import load_config


def quantize_model(model: nn.Module, 
                  calibration_data: torch.Tensor = None,
                  backend: str = 'fbgemm') -> nn.Module:
    """Quantize model for faster inference.
    
    Args:
        model: PyTorch model to quantize
        calibration_data: Data for calibration (optional)
        backend: Quantization backend
        
    Returns:
        Quantized model
    """
    print("Quantizing model...")
    
    # Set model to evaluation mode
    model.eval()
    
    # Prepare model for quantization
    model_prepared = torch.quantization.prepare(model)
    
    # Calibrate with sample data if provided
    if calibration_data is not None:
        print("Calibrating with sample data...")
        with torch.no_grad():
            model_prepared(calibration_data)
    
    # Convert to quantized model
    model_quantized = torch.quantization.convert(model_prepared)
    
    print("✓ Model quantized successfully")
    return model_quantized


def convert_to_torchscript(model: nn.Module, 
                          example_input: torch.Tensor,
                          optimize: bool = True) -> torch.jit.ScriptModule:
    """Convert model to TorchScript for deployment.
    
    Args:
        model: PyTorch model
        example_input: Example input tensor
        optimize: Whether to optimize the script module
        
    Returns:
        TorchScript module
    """
    print("Converting to TorchScript...")
    
    model.eval()
    
    # Trace the model
    with torch.no_grad():
        traced_model = torch.jit.trace(model, example_input)
    
    # Optimize if requested
    if optimize:
        traced_model = torch.jit.optimize_for_inference(traced_model)
    
    print("✓ Model converted to TorchScript")
    return traced_model


def convert_to_onnx(model: nn.Module,
                   example_input: torch.Tensor,
                   output_path: str,
                   opset_version: int = 11) -> None:
    """Convert model to ONNX format.
    
    Args:
        model: PyTorch model
        example_input: Example input tensor
        output_path: Path to save ONNX model
        opset_version: ONNX opset version
    """
    print("Converting to ONNX...")
    
    model.eval()
    
    # Export to ONNX
    torch.onnx.export(
        model,
        example_input,
        output_path,
        export_params=True,
        opset_version=opset_version,
        do_constant_folding=True,
        input_names=['input'],
        output_names=['output'],
        dynamic_axes={
            'input': {0: 'batch_size', 2: 'height', 3: 'width'},
            'output': {0: 'batch_size', 2: 'height', 3: 'width'}
        }
    )
    
    print(f"✓ Model exported to ONNX: {output_path}")


def prune_model(model: nn.Module, pruning_ratio: float = 0.2) -> nn.Module:
    """Apply magnitude-based pruning to model.
    
    Args:
        model: PyTorch model
        pruning_ratio: Ratio of weights to prune
        
    Returns:
        Pruned model
    """
    print(f"Pruning model (ratio: {pruning_ratio})...")
    
    import torch.nn.utils.prune as prune
    
    # Collect all conv and linear layers
    modules_to_prune = []
    for module in model.modules():
        if isinstance(module, (nn.Conv2d, nn.Linear)):
            modules_to_prune.append((module, 'weight'))
    
    # Apply global magnitude pruning
    prune.global_unstructured(
        modules_to_prune,
        pruning_method=prune.L1Unstructured,
        amount=pruning_ratio,
    )
    
    # Remove pruning reparameterization to make pruning permanent
    for module, param_name in modules_to_prune:
        prune.remove(module, param_name)
    
    print("✓ Model pruned successfully")
    return model


def benchmark_optimized_model(original_model: nn.Module,
                             optimized_model: nn.Module,
                             input_shape: tuple,
                             device: str = 'cpu',
                             num_runs: int = 100) -> dict:
    """Benchmark original vs optimized model.
    
    Args:
        original_model: Original model
        optimized_model: Optimized model
        input_shape: Input tensor shape
        device: Device to run on
        num_runs: Number of benchmark runs
        
    Returns:
        Benchmark results
    """
    import time
    
    def benchmark_model(model, name):
        model.eval()
        dummy_input = torch.randn(input_shape).to(device)
        
        # Warmup
        with torch.no_grad():
            for _ in range(10):
                _ = model(dummy_input)
        
        # Benchmark
        start_time = time.time()
        with torch.no_grad():
            for _ in range(num_runs):
                _ = model(dummy_input)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / num_runs
        fps = 1.0 / avg_time
        
        return {
            'avg_time': avg_time,
            'fps': fps,
            'total_time': end_time - start_time
        }
    
    print("Benchmarking models...")
    
    original_results = benchmark_model(original_model, "Original")
    optimized_results = benchmark_model(optimized_model, "Optimized")
    
    speedup = original_results['avg_time'] / optimized_results['avg_time']
    
    results = {
        'original': original_results,
        'optimized': optimized_results,
        'speedup': speedup
    }
    
    print(f"Original model: {original_results['fps']:.1f} FPS")
    print(f"Optimized model: {optimized_results['fps']:.1f} FPS")
    print(f"Speedup: {speedup:.2f}x")
    
    return results


def main():
    """Main optimization function."""
    parser = argparse.ArgumentParser(description='Optimize student models for deployment')
    parser.add_argument('--config', type=str, default='config.yaml',
                       help='Path to configuration file')
    parser.add_argument('--model', type=str, default='small',
                       choices=['micro', 'small', 'medium', 'large', 'efficient_0.5x', 'efficient_1.0x'],
                       help='Student model to optimize')
    parser.add_argument('--output-dir', type=str, default='results/optimized_models',
                       help='Output directory for optimized models')
    parser.add_argument('--device', type=str, default='cpu',
                       choices=['cpu', 'cuda'], help='Device to use')
    parser.add_argument('--input-size', nargs=2, type=int, default=[256, 256],
                       help='Input size for optimization (H W)')
    parser.add_argument('--batch-size', type=int, default=1,
                       help='Batch size for optimization')
    
    # Optimization options
    parser.add_argument('--quantize', action='store_true',
                       help='Apply quantization')
    parser.add_argument('--torchscript', action='store_true',
                       help='Convert to TorchScript')
    parser.add_argument('--onnx', action='store_true',
                       help='Convert to ONNX')
    parser.add_argument('--prune', type=float, default=0.0,
                       help='Pruning ratio (0.0 to disable)')
    
    args = parser.parse_args()
    
    # Set device
    device = args.device
    if device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        device = 'cpu'
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("Student Model Optimization")
    print("=" * 50)
    print(f"Model: {args.model}")
    print(f"Device: {device}")
    print(f"Input size: {args.input_size}")
    print(f"Optimizations: ", end="")
    
    optimizations = []
    if args.quantize:
        optimizations.append("Quantization")
    if args.torchscript:
        optimizations.append("TorchScript")
    if args.onnx:
        optimizations.append("ONNX")
    if args.prune > 0:
        optimizations.append(f"Pruning({args.prune})")
    
    print(", ".join(optimizations) if optimizations else "None")
    print()
    
    # Create student models
    models = create_optimized_student_models()
    
    if args.model not in models:
        print(f"Error: Model '{args.model}' not found")
        return
    
    # Get the selected model
    original_model = models[args.model].to(device)
    
    print(f"Original model parameters: {sum(p.numel() for p in original_model.parameters()):,}")
    
    # Create example input
    input_shape = (args.batch_size, 3, args.input_size[0], args.input_size[1])
    example_input = torch.randn(input_shape).to(device)
    
    # Start with the original model
    optimized_model = original_model
    
    # Apply pruning if requested
    if args.prune > 0:
        optimized_model = prune_model(optimized_model, args.prune)
    
    # Apply quantization if requested
    if args.quantize:
        # Note: Quantization typically works better on CPU
        if device == 'cuda':
            print("Warning: Quantization works better on CPU. Consider using --device cpu")
        
        try:
            optimized_model = quantize_model(optimized_model, example_input)
        except Exception as e:
            print(f"Quantization failed: {e}")
    
    # Convert to TorchScript if requested
    if args.torchscript:
        try:
            script_model = convert_to_torchscript(optimized_model, example_input)
            
            # Save TorchScript model
            script_path = os.path.join(args.output_dir, f"{args.model}_torchscript.pt")
            script_model.save(script_path)
            print(f"TorchScript model saved: {script_path}")
            
            # Use script model for further optimization
            optimized_model = script_model
            
        except Exception as e:
            print(f"TorchScript conversion failed: {e}")
    
    # Convert to ONNX if requested
    if args.onnx:
        try:
            onnx_path = os.path.join(args.output_dir, f"{args.model}_model.onnx")
            convert_to_onnx(optimized_model, example_input, onnx_path)
            
        except Exception as e:
            print(f"ONNX conversion failed: {e}")
    
    # Benchmark the optimized model
    print("\nBenchmarking...")
    try:
        benchmark_results = benchmark_optimized_model(
            original_model, optimized_model, input_shape, device
        )
        
        # Save benchmark results
        import json
        results_path = os.path.join(args.output_dir, f"{args.model}_benchmark.json")
        with open(results_path, 'w') as f:
            json.dump(benchmark_results, f, indent=2)
        
        print(f"Benchmark results saved: {results_path}")
        
    except Exception as e:
        print(f"Benchmarking failed: {e}")
    
    # Save the final optimized PyTorch model
    if not args.torchscript:  # Don't save if already saved as TorchScript
        model_path = os.path.join(args.output_dir, f"{args.model}_optimized.pth")
        torch.save({
            'model_state_dict': optimized_model.state_dict(),
            'model_name': args.model,
            'optimizations': optimizations,
            'input_size': args.input_size
        }, model_path)
        print(f"Optimized model saved: {model_path}")
    
    print("\nOptimization completed!")


if __name__ == '__main__':
    main()
