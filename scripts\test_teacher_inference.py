"""Script to test teacher model inference."""

import os
import argparse
import torch
import cv2
import numpy as np
from pathlib import Path
import sys
import time

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.models.teacher_models import TeacherModelManager
from src.utils.config import load_config
from src.utils.metrics import ImageMetrics, calculate_fps


def load_and_preprocess_image(image_path: str, target_size: tuple = None) -> torch.Tensor:
    """Load and preprocess image for inference.
    
    Args:
        image_path: Path to input image
        target_size: Target size (H, W) for resizing
        
    Returns:
        Preprocessed image tensor
    """
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image: {image_path}")
    
    # Convert BGR to RGB
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Resize if target size is specified
    if target_size:
        image = cv2.resize(image, (target_size[1], target_size[0]), 
                          interpolation=cv2.INTER_CUBIC)
    
    # Convert to float and normalize to [0, 1]
    image = image.astype(np.float32) / 255.0
    
    # Convert to tensor (C, H, W) and add batch dimension
    tensor = torch.from_numpy(image.transpose(2, 0, 1)).unsqueeze(0)
    
    return tensor


def save_output_image(tensor: torch.Tensor, output_path: str):
    """Save tensor as image.
    
    Args:
        tensor: Output tensor (1, C, H, W)
        output_path: Path to save image
    """
    # Remove batch dimension and convert to numpy
    image = tensor.squeeze(0).cpu().numpy()
    
    # Convert from CHW to HWC
    image = image.transpose(1, 2, 0)
    
    # Clip values to [0, 1] and convert to uint8
    image = np.clip(image, 0, 1)
    image = (image * 255).astype(np.uint8)
    
    # Convert RGB to BGR for OpenCV
    image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
    
    # Save image
    cv2.imwrite(output_path, image)


def test_teacher_inference(config, 
                          model_name: str,
                          input_image: str,
                          output_dir: str,
                          device: str = 'cuda'):
    """Test teacher model inference on a single image.
    
    Args:
        config: Configuration object
        model_name: Name of teacher model
        input_image: Path to input image
        output_dir: Directory to save outputs
        device: Device to run inference on
    """
    print(f"Testing {model_name} teacher model inference...")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Load teacher model
    teacher_manager = TeacherModelManager(model_name, device)
    
    # Get model configuration
    teacher_config = config.get('model.teacher', {})
    pretrained_path = teacher_config.get('pretrained_path', 
                                        f'models/pretrained/{model_name}_teacher.pth')
    
    # Load pretrained model
    model = teacher_manager.load_pretrained(pretrained_path)
    
    # Load and preprocess input image
    input_size = config.get('dataset.input_size', [256, 256])
    input_tensor = load_and_preprocess_image(input_image, tuple(input_size))
    input_tensor = input_tensor.to(device)
    
    print(f"Input image shape: {input_tensor.shape}")
    
    # Measure inference time
    model.eval()
    
    # Warm up
    with torch.no_grad():
        for _ in range(5):
            _ = model(input_tensor)
    
    # Measure inference time
    torch.cuda.synchronize() if device == 'cuda' else None
    start_time = time.time()
    
    with torch.no_grad():
        output_tensor = model(input_tensor)
    
    torch.cuda.synchronize() if device == 'cuda' else None
    end_time = time.time()
    
    inference_time = end_time - start_time
    print(f"Inference time: {inference_time:.4f} seconds")
    print(f"Output image shape: {output_tensor.shape}")
    
    # Save input and output images
    input_save_path = os.path.join(output_dir, f"{model_name}_input.png")
    output_save_path = os.path.join(output_dir, f"{model_name}_output.png")
    
    save_output_image(input_tensor, input_save_path)
    save_output_image(output_tensor, output_save_path)
    
    print(f"Saved input image: {input_save_path}")
    print(f"Saved output image: {output_save_path}")
    
    # Calculate metrics (comparing output to input as reference)
    metrics = ImageMetrics(device)
    
    # Calculate SSIM and PSNR
    ssim_score = metrics.calculate_ssim(output_tensor, input_tensor)
    psnr_score = metrics.calculate_psnr(output_tensor, input_tensor)
    
    print(f"SSIM (output vs input): {ssim_score:.4f}")
    print(f"PSNR (output vs input): {psnr_score:.2f} dB")
    
    # Test intermediate features
    print("\nTesting intermediate features extraction...")
    features = teacher_manager.get_intermediate_features(input_tensor)
    
    for name, feature in features.items():
        print(f"Feature '{name}': {feature.shape}")
    
    # Calculate FPS
    print("\nMeasuring FPS performance...")
    fps = calculate_fps(model, input_tensor.shape, device, num_runs=50)
    print(f"Average FPS: {fps:.2f}")
    
    return {
        'inference_time': inference_time,
        'fps': fps,
        'ssim': ssim_score,
        'psnr': psnr_score,
        'input_shape': input_tensor.shape,
        'output_shape': output_tensor.shape
    }


def create_test_image(output_path: str, size: tuple = (256, 256)):
    """Create a test image for inference testing.
    
    Args:
        output_path: Path to save test image
        size: Image size (H, W)
    """
    height, width = size
    
    # Create a test image with various patterns
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Add gradient background
    for i in range(height):
        for j in range(width):
            image[i, j] = [i * 255 // height, j * 255 // width, 128]
    
    # Add some geometric shapes
    cv2.rectangle(image, (50, 50), (150, 100), (255, 0, 0), -1)
    cv2.circle(image, (200, 150), 30, (0, 255, 0), -1)
    
    # Add text
    cv2.putText(image, "Test Image", (50, 200), 
               cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    # Save image
    cv2.imwrite(output_path, image)
    print(f"Created test image: {output_path}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Test teacher model inference')
    parser.add_argument('--config', type=str, default='config.yaml',
                       help='Path to configuration file')
    parser.add_argument('--model', type=str, choices=['edsr', 'esrgan', 'srresnet'],
                       default='edsr', help='Teacher model to test')
    parser.add_argument('--input', type=str, default=None,
                       help='Input image path')
    parser.add_argument('--output-dir', type=str, default='results/teacher_inference',
                       help='Output directory')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cpu', 'cuda'], help='Device to use')
    parser.add_argument('--create-test-image', action='store_true',
                       help='Create a test image if no input provided')
    
    args = parser.parse_args()
    
    # Load configuration
    try:
        config = load_config(args.config)
    except Exception as e:
        print(f"Error loading config: {e}")
        return
    
    # Set device
    device = args.device
    if device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        device = 'cpu'
    
    # Handle input image
    input_image = args.input
    
    if input_image is None:
        if args.create_test_image:
            # Create test image
            test_image_path = os.path.join(args.output_dir, "test_input.png")
            os.makedirs(args.output_dir, exist_ok=True)
            create_test_image(test_image_path)
            input_image = test_image_path
        else:
            print("No input image provided. Use --input or --create-test-image")
            return
    
    if not os.path.exists(input_image):
        print(f"Input image not found: {input_image}")
        return
    
    print("Teacher Model Inference Test")
    print("=" * 50)
    print(f"Model: {args.model}")
    print(f"Input: {input_image}")
    print(f"Output directory: {args.output_dir}")
    print(f"Device: {device}")
    print()
    
    try:
        # Test inference
        results = test_teacher_inference(
            config=config,
            model_name=args.model,
            input_image=input_image,
            output_dir=args.output_dir,
            device=device
        )
        
        print("\n" + "=" * 50)
        print("Test Results Summary")
        print("=" * 50)
        print(f"Model: {args.model.upper()}")
        print(f"Inference time: {results['inference_time']:.4f} seconds")
        print(f"FPS: {results['fps']:.2f}")
        print(f"SSIM: {results['ssim']:.4f}")
        print(f"PSNR: {results['psnr']:.2f} dB")
        print(f"Input shape: {results['input_shape']}")
        print(f"Output shape: {results['output_shape']}")
        
        print("\n✓ Teacher model inference test completed successfully!")
        
    except Exception as e:
        print(f"✗ Teacher model inference test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
